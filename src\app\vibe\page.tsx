import Link from 'next/link';

export default function Vibe() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-pink-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-16">
        {/* Header */}
        <header className="text-center mb-16">
          <Link href="/context-map" className="text-purple-600 hover:text-purple-800 mb-4 inline-block">
            ← Back to Context Map
          </Link>
          <h1 className="text-5xl font-bold text-gray-900 dark:text-white mb-4">
            ✨ Vibe & Experience
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Define the tone, personality, and user experience for your AI project
          </p>
        </header>

        {/* Coming Soon Card */}
        <div className="max-w-2xl mx-auto">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-12 text-center">
            <div className="text-6xl mb-6">🚧</div>
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Coming Soon
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
              The Vibe & Experience module is currently under development. 
              This module will help you define the personality, tone, and user experience aspects of your AI project.
            </p>
            
            <div className="space-y-4 text-left bg-gray-50 dark:bg-gray-700 rounded-lg p-6 mb-8">
              <h3 className="font-semibold text-gray-900 dark:text-white">This module will include:</h3>
              <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                <li>• Personality and tone definition</li>
                <li>• User experience preferences</li>
                <li>• Communication style guidelines</li>
                <li>• Brand voice and messaging</li>
                <li>• Interaction flow design</li>
                <li>• Emotional intelligence requirements</li>
              </ul>
            </div>

            <div className="flex justify-center space-x-4">
              <Link
                href="/context-map"
                className="px-6 py-3 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors"
              >
                ← Previous Module
              </Link>
              <Link
                href="/"
                className="px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
              >
                Back to Home
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
