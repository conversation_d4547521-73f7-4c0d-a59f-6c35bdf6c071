'use client';

import ModuleLayout from '@/components/ModuleLayout';
import SmartQuestion from '@/components/SmartQuestion';
import OutputPanel from '@/components/OutputPanel';
import { useContextStore } from '@/store/contextStore';

export default function EmotionalTone() {
  const { emotionalTone, updateEmotionalTone } = useContextStore();

  const questions = [
    {
      id: 'personality',
      question: 'What personality should your AI system have?',
      questionAr: 'ما هي الشخصية التي يجب أن يتمتع بها نظام الذكاء الاصطناعي؟',
      placeholder: 'e.g., Professional and helpful, Friendly and casual, Formal and authoritative...',
      placeholderAr: 'مثال: مهني ومفيد، ودود وغير رسمي، رسمي وموثوق...',
      aiSuggestion: 'Consider your brand values, target audience expectations, and the context of use.',
      aiSuggestionAr: 'فكر في قيم علامتك التجارية وتوقعات الجمهور المستهدف وسياق الاستخدام.',
      promptTemplate: 'Help me define the ideal AI personality for this description: "{answer}". Suggest specific traits and behaviors.'
    },
    {
      id: 'communicationStyle',
      question: 'How should your AI communicate with users?',
      questionAr: 'كيف يجب أن يتواصل الذكاء الاصطناعي مع المستخدمين؟',
      placeholder: 'e.g., Clear and concise, Detailed explanations, Step-by-step guidance...',
      placeholderAr: 'مثال: واضح ومختصر، شروحات مفصلة، إرشادات خطوة بخطوة...',
      aiSuggestion: 'Think about communication preferences, complexity levels, and user expertise.',
      aiSuggestionAr: 'فكر في تفضيلات التواصل ومستويات التعقيد وخبرة المستخدم.',
      promptTemplate: 'Analyze this communication style for an AI system: "{answer}". How can I implement this effectively?'
    },
    {
      id: 'userExperience',
      question: 'What kind of user experience do you want to create?',
      questionAr: 'ما نوع تجربة المستخدم التي تريد إنشاؤها؟',
      placeholder: 'e.g., Seamless and intuitive, Educational and informative, Fast and efficient...',
      placeholderAr: 'مثال: سلسة وبديهية، تعليمية وإعلامية، سريعة وفعالة...',
      aiSuggestion: 'Focus on user goals, pain points, and the emotional journey you want to create.',
      aiSuggestionAr: 'ركز على أهداف المستخدم ونقاط الألم والرحلة العاطفية التي تريد إنشاؤها.',
      promptTemplate: 'Help me design a user experience strategy based on: "{answer}". What specific elements should I include?'
    },
    {
      id: 'brandVoice',
      question: 'What is your brand voice and messaging approach?',
      questionAr: 'ما هو صوت علامتك التجارية ونهج الرسائل؟',
      placeholder: 'e.g., Innovative and forward-thinking, Trustworthy and reliable, Creative and inspiring...',
      placeholderAr: 'مثال: مبتكر ومتطلع للمستقبل، جدير بالثقة وموثوق، إبداعي وملهم...',
      aiSuggestion: 'Align with your company values and differentiate from competitors.',
      aiSuggestionAr: 'اتماشى مع قيم شركتك وميز نفسك عن المنافسين.',
      promptTemplate: 'Develop a brand voice strategy for: "{answer}". How should this translate into AI interactions?'
    }
  ];

  const handleFieldChange = (field: keyof typeof emotionalTone, value: string) => {
    updateEmotionalTone({ [field]: value });
  };

  return (
    <ModuleLayout
      title="Emotional Tone & Experience"
      titleAr="النبرة العاطفية والتجربة"
      subtitle="Define the personality, tone, and user experience for your AI"
      subtitleAr="حدد الشخصية والنبرة وتجربة المستخدم للذكاء الاصطناعي"
      emoji="✨"
      backLink={{
        href: "/context-map",
        label: "← Back to Context Map",
        labelAr: "← العودة لخريطة السياق"
      }}
      nextLink={{
        href: "/technical-layer",
        label: "Next: Technical Layer →",
        labelAr: "التالي: الطبقة التقنية ←"
      }}
      rightPanel={
        <OutputPanel 
          moduleData={emotionalTone}
          moduleName="Emotional Tone & Experience"
          moduleNameAr="النبرة العاطفية والتجربة"
        />
      }
    >
      <div className="space-y-6">
        {questions.map((question) => (
          <SmartQuestion
            key={question.id}
            id={question.id}
            question={question.question}
            questionAr={question.questionAr}
            placeholder={question.placeholder}
            placeholderAr={question.placeholderAr}
            value={emotionalTone[question.id as keyof typeof emotionalTone] || ''}
            onChange={(value) => handleFieldChange(question.id as keyof typeof emotionalTone, value)}
            aiSuggestion={question.aiSuggestion}
            aiSuggestionAr={question.aiSuggestionAr}
            promptTemplate={question.promptTemplate}
          />
        ))}
      </div>
    </ModuleLayout>
  );
}
