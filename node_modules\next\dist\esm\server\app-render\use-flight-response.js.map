{"version": 3, "sources": ["../../../src/server/app-render/use-flight-response.tsx"], "names": ["htmlEscapeJsonString", "isEdgeRuntime", "process", "env", "NEXT_RUNTIME", "INLINE_FLIGHT_PAYLOAD_BOOTSTRAP", "INLINE_FLIGHT_PAYLOAD_DATA", "INLINE_FLIGHT_PAYLOAD_FORM_STATE", "flightResponses", "WeakMap", "encoder", "TextEncoder", "useFlightStream", "flightStream", "clientReferenceManifest", "nonce", "response", "get", "createFromReadableStream", "TURBOPACK", "require", "newResponse", "ssrManifest", "moduleLoading", "moduleMap", "edgeSSRModuleMapping", "ssrModuleMapping", "set", "flightRenderComplete", "flightReader", "<PERSON><PERSON><PERSON><PERSON>", "done", "read", "createInlinedDataReadableStream", "formState", "startScriptTag", "JSON", "stringify", "decoder", "TextDecoder", "fatal", "decoderOptions", "stream", "readable", "ReadableStream", "type", "start", "controller", "writeInitialInstructions", "error", "pull", "value", "tail", "decode", "length", "writeFlightDataInstruction", "close", "chunkAsString", "scriptStart", "enqueue", "encode"], "mappings": "AAGA,SAASA,oBAAoB,QAAQ,gBAAe;AAEpD,MAAMC,gBAAgBC,QAAQC,GAAG,CAACC,YAAY,KAAK;AAEnD,MAAMC,kCAAkC;AACxC,MAAMC,6BAA6B;AACnC,MAAMC,mCAAmC;AAEzC,MAAMC,kBAAkB,IAAIC;AAC5B,MAAMC,UAAU,IAAIC;AAEpB;;;CAGC,GACD,OAAO,SAASC,gBACdC,YAA+B,EAC/BC,uBAAgD,EAChDC,KAAc;IAEd,MAAMC,WAAWR,gBAAgBS,GAAG,CAACJ;IAErC,IAAIG,UAAU;QACZ,OAAOA;IACT;IAEA,wGAAwG;IACxG,IAAIE;IACJ,uGAAuG;IACvG,IAAIhB,QAAQC,GAAG,CAACgB,SAAS,EAAE;QACzBD,2BACE,6DAA6D;QAC7DE,QAAQ,0CAA0CF,wBAAwB;IAC9E,OAAO;QACLA,2BACE,6DAA6D;QAC7DE,QAAQ,wCAAwCF,wBAAwB;IAC5E;IAEA,MAAMG,cAAcH,yBAAyBL,cAAc;QACzDS,aAAa;YACXC,eAAeT,wBAAwBS,aAAa;YACpDC,WAAWvB,gBACPa,wBAAwBW,oBAAoB,GAC5CX,wBAAwBY,gBAAgB;QAC9C;QACAX;IACF;IAEAP,gBAAgBmB,GAAG,CAACd,cAAcQ;IAElC,OAAOA;AACT;AAEA;;;;;;;;CAQC,GACD,OAAO,eAAeO,qBACpBf,YAAwC;IAExC,MAAMgB,eAAehB,aAAaiB,SAAS;IAE3C,MAAO,KAAM;QACX,MAAM,EAAEC,IAAI,EAAE,GAAG,MAAMF,aAAaG,IAAI;QACxC,IAAID,MAAM;YACR;QACF;IACF;AACF;AAEA;;;;;;;;CAQC,GACD,OAAO,SAASE,gCACdpB,YAAwC,EACxCE,KAAyB,EACzBmB,SAAyB;IAEzB,MAAMC,iBAAiBpB,QACnB,CAAC,cAAc,EAAEqB,KAAKC,SAAS,CAACtB,OAAO,CAAC,CAAC,GACzC;IAEJ,MAAMuB,UAAU,IAAIC,YAAY,SAAS;QAAEC,OAAO;IAAK;IACvD,MAAMC,iBAAiB;QAAEC,QAAQ;IAAK;IAEtC,MAAMb,eAAehB,aAAaiB,SAAS;IAE3C,MAAMa,WAAW,IAAIC,eAAe;QAClCC,MAAM;QACNC,OAAMC,UAAU;YACd,IAAI;gBACFC,yBAAyBD,YAAYZ,gBAAgBD;YACvD,EAAE,OAAOe,OAAO;gBACd,6DAA6D;gBAC7DF,WAAWE,KAAK,CAACA;YACnB;QACF;QACA,MAAMC,MAAKH,UAAU;YACnB,IAAI;gBACF,MAAM,EAAEhB,IAAI,EAAEoB,KAAK,EAAE,GAAG,MAAMtB,aAAaG,IAAI;gBAC/C,IAAID,MAAM;oBACR,MAAMqB,OAAOd,QAAQe,MAAM,CAACF,OAAO;wBAAET,QAAQ;oBAAM;oBACnD,IAAIU,KAAKE,MAAM,EAAE;wBACfC,2BAA2BR,YAAYZ,gBAAgBiB;oBACzD;oBACAL,WAAWS,KAAK;gBAClB,OAAO;oBACL,MAAMC,gBAAgBnB,QAAQe,MAAM,CAACF,OAAOV;oBAC5Cc,2BAA2BR,YAAYZ,gBAAgBsB;gBACzD;YACF,EAAE,OAAOR,OAAO;gBACd,6EAA6E;gBAC7E,+BAA+B;gBAC/BF,WAAWE,KAAK,CAACA;YACnB;QACF;IACF;IAEA,OAAON;AACT;AAEA,SAASK,yBACPD,UAA2C,EAC3CW,WAAmB,EACnBxB,SAAyB;IAEzBa,WAAWY,OAAO,CAChBjD,QAAQkD,MAAM,CACZ,CAAC,EAAEF,YAAY,uCAAuC,EAAE1D,qBACtDoC,KAAKC,SAAS,CAAC;QAAChC;KAAgC,GAChD,qBAAqB,EAAEL,qBACvBoC,KAAKC,SAAS,CAAC;QAAC9B;QAAkC2B;KAAU,GAC5D,UAAU,CAAC;AAGnB;AAEA,SAASqB,2BACPR,UAA2C,EAC3CW,WAAmB,EACnBD,aAAqB;IAErBV,WAAWY,OAAO,CAChBjD,QAAQkD,MAAM,CACZ,CAAC,EAAEF,YAAY,mBAAmB,EAAE1D,qBAClCoC,KAAKC,SAAS,CAAC;QAAC/B;QAA4BmD;KAAc,GAC1D,UAAU,CAAC;AAGnB"}