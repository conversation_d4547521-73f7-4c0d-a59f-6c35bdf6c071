'use client';

import Link from 'next/link';
import ThemeToggle from './ThemeToggle';
import LanguageToggle from './LanguageToggle';

interface HeaderProps {
  title: string;
  subtitle?: string;
  backLink?: {
    href: string;
    label: string;
  };
  emoji?: string;
}

export default function Header({ title, subtitle, backLink, emoji }: HeaderProps) {
  return (
    <header className="relative">
      {/* Controls - Fixed position in top right */}
      <div className="fixed top-4 right-4 z-50 flex space-x-2">
        <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-lg shadow-lg">
          <LanguageToggle />
        </div>
        <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-lg shadow-lg">
          <ThemeToggle />
        </div>
      </div>

      {/* Header Content */}
      <div className="text-center mb-8 pt-4">
        {backLink && (
          <Link 
            href={backLink.href} 
            className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mb-4 inline-block transition-colors"
          >
            {backLink.label}
          </Link>
        )}
        <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-2">
          {emoji && <span className="mr-2">{emoji}</span>}
          {title}
        </h1>
        {subtitle && (
          <p className="text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            {subtitle}
          </p>
        )}
      </div>
    </header>
  );
}
