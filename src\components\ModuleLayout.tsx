'use client';

import { ReactNode } from 'react';
import Header from './Header';
import ProgressIndicator from './ProgressIndicator';
import AutoSaveIndicator from './AutoSaveIndicator';
import { useContextStore } from '@/store/contextStore';

interface ModuleLayoutProps {
  title: string;
  titleAr: string;
  subtitle: string;
  subtitleAr: string;
  emoji: string;
  moduleKey?: string;
  backLink?: {
    href: string;
    label: string;
    labelAr: string;
  };
  nextLink?: {
    href: string;
    label: string;
    labelAr: string;
  };
  children: ReactNode;
  rightPanel: ReactNode;
}

export default function ModuleLayout({
  title,
  titleAr,
  subtitle,
  subtitleAr,
  emoji,
  moduleKey,
  backLink,
  nextLink,
  children,
  rightPanel
}: ModuleLayoutProps) {
  const { currentLanguage } = useContextStore();
  const isArabic = currentLanguage === 'ar';

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <Header
          title={isArabic ? titleAr : title}
          subtitle={isArabic ? subtitleAr : subtitle}
          emoji={emoji}
          backLink={backLink ? {
            href: backLink.href,
            label: isArabic ? backLink.labelAr : backLink.label
          } : undefined}
        />

        {/* Progress Indicator */}
        <ProgressIndicator currentModule={moduleKey} />

        {/* Main Content - Two Panel Layout */}
        <div className="grid lg:grid-cols-2 gap-8 max-w-7xl mx-auto">
          {/* Left Panel - Questions */}
          <div className={`space-y-6 ${isArabic ? 'lg:order-2' : 'lg:order-1'}`}>
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
              <div className="flex items-center mb-4">
                <span className="text-2xl mr-3">✍️</span>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  {isArabic ? 'الأسئلة الذكية' : 'Smart Questions'}
                </h2>
              </div>
              {children}
            </div>
          </div>

          {/* Right Panel - Outputs */}
          <div className={`space-y-6 ${isArabic ? 'lg:order-1' : 'lg:order-2'}`}>
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 sticky top-8">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <span className="text-2xl mr-3">📄</span>
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                    {isArabic ? 'المخرجات المجمّعة' : 'Generated Outputs'}
                  </h2>
                </div>
              </div>
              {rightPanel}
            </div>
          </div>
        </div>

        {/* Navigation */}
        {(backLink || nextLink) && (
          <div className="flex justify-between items-center mt-12 max-w-7xl mx-auto">
            {backLink ? (
              <a
                href={backLink.href}
                className="flex items-center px-6 py-3 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors"
              >
                <span className="mr-2">←</span>
                {isArabic ? backLink.labelAr : backLink.label}
              </a>
            ) : (
              <div></div>
            )}

            {nextLink && (
              <a
                href={nextLink.href}
                className="flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              >
                {isArabic ? nextLink.labelAr : nextLink.label}
                <span className="ml-2">→</span>
              </a>
            )}
          </div>
        )}

        {/* Auto-save Indicator */}
        <AutoSaveIndicator />
      </div>
    </div>
  );
}
