'use client';

import ModuleLayout from '@/components/ModuleLayout';
import SmartQuestion from '@/components/SmartQuestion';
import OutputPanel from '@/components/OutputPanel';
import { useContextStore } from '@/store/contextStore';

export default function ProjectDefinition() {
  const { projectDefinition, updateProjectDefinition } = useContextStore();

  const questions = [
    {
      id: 'name',
      question: 'What is the name of your AI project?',
      questionAr: 'ما هو اسم مشروع الذكاء الاصطناعي الخاص بك؟',
      placeholder: 'e.g., Smart Customer Support Bot, Content Generator AI, etc.',
      placeholderAr: 'مثال: بوت دعم العملاء الذكي، مولد المحتوى بالذكاء الاصطناعي، إلخ.',
      type: 'text' as const,
      aiSuggestion: 'Choose a clear, descriptive name that reflects your project\'s main function and target audience.',
      aiSuggestionAr: 'اختر اسماً واضحاً ووصفياً يعكس الوظيفة الرئيسية لمشروعك والجمهور المستهدف.',
      promptTemplate: 'Help me refine this AI project name: "{answer}". Suggest improvements for clarity and market appeal.'
    },
    {
      id: 'purpose',
      question: 'What is the main purpose or problem your AI project aims to solve?',
      questionAr: 'ما هو الهدف الرئيسي أو المشكلة التي يهدف مشروع الذكاء الاصطناعي لحلها؟',
      placeholder: 'Describe the core problem you want to address...',
      placeholderAr: 'صف المشكلة الأساسية التي تريد معالجتها...',
      aiSuggestion: 'Focus on a specific, measurable problem. Avoid being too broad or vague.',
      aiSuggestionAr: 'ركز على مشكلة محددة وقابلة للقياس. تجنب أن تكون عاماً أو غامضاً.',
      promptTemplate: 'Analyze this problem statement for an AI project: "{answer}". Help me make it more specific and actionable.'
    },
    {
      id: 'targetUsers',
      question: 'Who are the primary users or beneficiaries of this project?',
      questionAr: 'من هم المستخدمون الأساسيون أو المستفيدون من هذا المشروع؟',
      placeholder: 'e.g., Customer service teams, Content creators, Students, etc.',
      placeholderAr: 'مثال: فرق خدمة العملاء، منشئو المحتوى، الطلاب، إلخ.',
      aiSuggestion: 'Be specific about user demographics, roles, and their current pain points.',
      aiSuggestionAr: 'كن محدداً حول التركيبة السكانية للمستخدمين وأدوارهم ونقاط الألم الحالية لديهم.',
      promptTemplate: 'Help me create detailed user personas for this target audience: "{answer}". Include their needs and challenges.'
    }
  ];

  const handleFieldChange = (field: keyof typeof projectDefinition, value: string) => {
    updateProjectDefinition({ [field]: value });
  };

  return (
    <ModuleLayout
      title="Project Definition"
      titleAr="تعريف المشروع"
      subtitle="Define the scope, users, and goals of your AI project"
      subtitleAr="حدد نطاق مشروعك والمستخدمين والأهداف"
      emoji="🎯"
      backLink={{
        href: "/",
        label: "← Back to Home",
        labelAr: "← العودة للرئيسية"
      }}
      nextLink={{
        href: "/context-map",
        label: "Next: Context Map →",
        labelAr: "التالي: خريطة السياق ←"
      }}
      rightPanel={
        <OutputPanel
          moduleData={projectDefinition}
          moduleName="Project Definition"
          moduleNameAr="تعريف المشروع"
        />
      }
    >
      <div className="space-y-6">
        {questions.map((question) => (
          <SmartQuestion
            key={question.id}
            id={question.id}
            question={question.question}
            questionAr={question.questionAr}
            placeholder={question.placeholder}
            placeholderAr={question.placeholderAr}
            value={projectDefinition[question.id as keyof typeof projectDefinition] || ''}
            onChange={(value) => handleFieldChange(question.id as keyof typeof projectDefinition, value)}
            type={question.type}
            aiSuggestion={question.aiSuggestion}
            aiSuggestionAr={question.aiSuggestionAr}
            promptTemplate={question.promptTemplate}
          />
        ))}
      </div>
    </ModuleLayout>
  );
}
