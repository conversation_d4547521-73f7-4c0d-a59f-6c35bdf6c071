'use client';

import { useState } from 'react';
import Link from 'next/link';

interface ProjectData {
  name: string;
  purpose: string;
  targetUsers: string;
  goals: string;
  scope: string;
  timeline: string;
}

export default function ProjectDefinition() {
  const [projectData, setProjectData] = useState<ProjectData>({
    name: '',
    purpose: '',
    targetUsers: '',
    goals: '',
    scope: '',
    timeline: ''
  });

  const [currentStep, setCurrentStep] = useState(0);

  const questions = [
    {
      id: 'name',
      title: 'Project Name',
      question: 'What is the name of your AI project?',
      placeholder: 'e.g., Smart Customer Support Bot, Content Generator AI, etc.',
      type: 'text'
    },
    {
      id: 'purpose',
      title: 'Project Purpose',
      question: 'What is the main purpose or problem your AI project aims to solve?',
      placeholder: 'Describe the core problem you want to address...',
      type: 'textarea'
    },
    {
      id: 'targetUsers',
      title: 'Target Users',
      question: 'Who are the primary users or beneficiaries of this project?',
      placeholder: 'e.g., Customer service teams, Content creators, Students, etc.',
      type: 'textarea'
    },
    {
      id: 'goals',
      title: 'Project Goals',
      question: 'What are the specific goals and success metrics for this project?',
      placeholder: 'List your measurable objectives and KPIs...',
      type: 'textarea'
    },
    {
      id: 'scope',
      title: 'Project Scope',
      question: 'What is included and excluded from this project scope?',
      placeholder: 'Define boundaries, features, and limitations...',
      type: 'textarea'
    },
    {
      id: 'timeline',
      title: 'Timeline',
      question: 'What is your expected timeline for this project?',
      placeholder: 'e.g., 3 months for MVP, 6 months for full release...',
      type: 'text'
    }
  ];

  const handleInputChange = (field: keyof ProjectData, value: string) => {
    setProjectData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const nextStep = () => {
    if (currentStep < questions.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const generateOutput = () => {
    const markdown = `# Project Definition

## Project Name
${projectData.name}

## Purpose
${projectData.purpose}

## Target Users
${projectData.targetUsers}

## Goals
${projectData.goals}

## Scope
${projectData.scope}

## Timeline
${projectData.timeline}
`;

    navigator.clipboard.writeText(markdown);
    alert('Project definition copied to clipboard!');
  };

  const currentQuestion = questions[currentStep];
  const progress = ((currentStep + 1) / questions.length) * 100;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <header className="text-center mb-8">
          <Link href="/" className="text-blue-600 hover:text-blue-800 mb-4 inline-block">
            ← Back to Home
          </Link>
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-2">
            🎯 Project Definition
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            Define the scope, users, and goals of your AI project
          </p>
        </header>

        {/* Progress Bar */}
        <div className="max-w-2xl mx-auto mb-8">
          <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
            <span>Step {currentStep + 1} of {questions.length}</span>
            <span>{Math.round(progress)}% Complete</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        </div>

        {/* Question Card */}
        <div className="max-w-2xl mx-auto">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
              {currentQuestion.title}
            </h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              {currentQuestion.question}
            </p>
            
            {currentQuestion.type === 'textarea' ? (
              <textarea
                value={projectData[currentQuestion.id as keyof ProjectData]}
                onChange={(e) => handleInputChange(currentQuestion.id as keyof ProjectData, e.target.value)}
                placeholder={currentQuestion.placeholder}
                className="w-full p-4 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white resize-none"
                rows={6}
              />
            ) : (
              <input
                type="text"
                value={projectData[currentQuestion.id as keyof ProjectData]}
                onChange={(e) => handleInputChange(currentQuestion.id as keyof ProjectData, e.target.value)}
                placeholder={currentQuestion.placeholder}
                className="w-full p-4 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
            )}
          </div>

          {/* Navigation Buttons */}
          <div className="flex justify-between">
            <button
              onClick={prevStep}
              disabled={currentStep === 0}
              className="px-6 py-3 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors"
            >
              Previous
            </button>
            
            {currentStep === questions.length - 1 ? (
              <div className="space-x-4">
                <button
                  onClick={generateOutput}
                  className="px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
                >
                  Copy to Clipboard
                </button>
                <Link
                  href="/context-map"
                  className="inline-block px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                >
                  Next Module →
                </Link>
              </div>
            ) : (
              <button
                onClick={nextStep}
                className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              >
                Next
              </button>
            )}
          </div>
        </div>

        {/* Output Preview */}
        {Object.values(projectData).some(value => value.trim() !== '') && (
          <div className="max-w-2xl mx-auto mt-12">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              📋 Preview Output
            </h3>
            <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-6 font-mono text-sm">
              <pre className="whitespace-pre-wrap text-gray-800 dark:text-gray-200">
{`# Project Definition

## Project Name
${projectData.name || '[Not specified]'}

## Purpose
${projectData.purpose || '[Not specified]'}

## Target Users
${projectData.targetUsers || '[Not specified]'}

## Goals
${projectData.goals || '[Not specified]'}

## Scope
${projectData.scope || '[Not specified]'}

## Timeline
${projectData.timeline || '[Not specified]'}`}
              </pre>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
