'use client';

import { useState, useEffect } from 'react';
import { useContextStore } from '@/store/contextStore';

export default function AutoSaveIndicator() {
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [saving, setSaving] = useState(false);
  const { currentLanguage } = useContextStore();
  const isArabic = currentLanguage === 'ar';

  // Simulate auto-save functionality
  useEffect(() => {
    const interval = setInterval(() => {
      setSaving(true);
      setTimeout(() => {
        setLastSaved(new Date());
        setSaving(false);
      }, 500);
    }, 10000); // Auto-save every 10 seconds

    return () => clearInterval(interval);
  }, []);

  if (!lastSaved && !saving) return null;

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg px-4 py-2 flex items-center space-x-2">
        {saving ? (
          <>
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {isArabic ? 'جاري الحفظ...' : 'Saving...'}
            </span>
          </>
        ) : (
          <>
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {isArabic ? 'تم الحفظ' : 'Saved'} {lastSaved?.toLocaleTimeString()}
            </span>
          </>
        )}
      </div>
    </div>
  );
}
