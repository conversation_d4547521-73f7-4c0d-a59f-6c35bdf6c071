/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/final-preview/page";
exports.ids = ["app/final-preview/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffinal-preview%2Fpage&page=%2Ffinal-preview%2Fpage&appPaths=%2Ffinal-preview%2Fpage&pagePath=private-next-app-dir%2Ffinal-preview%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffinal-preview%2Fpage&page=%2Ffinal-preview%2Fpage&appPaths=%2Ffinal-preview%2Fpage&pagePath=private-next-app-dir%2Ffinal-preview%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'final-preview',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/final-preview/page.tsx */ \"(rsc)/./src/app/final-preview/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/final-preview/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/final-preview/page\",\n        pathname: \"/final-preview\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffinal-preview%2Fpage&page=%2Ffinal-preview%2Fpage&appPaths=%2Ffinal-preview%2Fpage&pagePath=private-next-app-dir%2Ffinal-preview%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cfinal-preview%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cfinal-preview%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/final-preview/page.tsx */ \"(ssr)/./src/app/final-preview/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2ZhaXNzJTVDJTVDRGVza3RvcCU1QyU1Q0NvbnRleHRLaXQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNmaW5hbC1wcmV2aWV3JTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRLQUE2RyIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbnRleHRraXQvPzhiNTciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxmYWlzc1xcXFxEZXNrdG9wXFxcXENvbnRleHRLaXRcXFxcc3JjXFxcXGFwcFxcXFxmaW5hbC1wcmV2aWV3XFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cfinal-preview%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ThemeProvider.tsx */ \"(ssr)/./src/components/ThemeProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2ZhaXNzJTVDJTVDRGVza3RvcCU1QyU1Q0NvbnRleHRLaXQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNmYWlzcyU1QyU1Q0Rlc2t0b3AlNUMlNUNDb250ZXh0S2l0JTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q1RoZW1lUHJvdmlkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyVGhlbWVQcm92aWRlciUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0xBQWlKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29udGV4dGtpdC8/ODQzMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlRoZW1lUHJvdmlkZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxmYWlzc1xcXFxEZXNrdG9wXFxcXENvbnRleHRLaXRcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcVGhlbWVQcm92aWRlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/final-preview/page.tsx":
/*!****************************************!*\
  !*** ./src/app/final-preview/page.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FinalPreview)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Header */ \"(ssr)/./src/components/Header.tsx\");\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction FinalPreview() {\n    const { getAllData, currentLanguage, outputFormat, setOutputFormat, projectDefinition, contextMap, emotionalTone, technicalLayer, legalRisk } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_3__.useContextStore)();\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isArabic = currentLanguage === \"ar\";\n    const modules = [\n        {\n            key: \"projectDefinition\",\n            name: \"Project Definition\",\n            nameAr: \"تعريف المشروع\",\n            emoji: \"\\uD83C\\uDFAF\",\n            data: projectDefinition,\n            href: \"/project-definition\"\n        },\n        {\n            key: \"contextMap\",\n            name: \"Context Map\",\n            nameAr: \"خريطة السياق\",\n            emoji: \"\\uD83D\\uDDFA️\",\n            data: contextMap,\n            href: \"/context-map\"\n        },\n        {\n            key: \"emotionalTone\",\n            name: \"Emotional Tone\",\n            nameAr: \"النبرة العاطفية\",\n            emoji: \"✨\",\n            data: emotionalTone,\n            href: \"/emotional-tone\"\n        },\n        {\n            key: \"technicalLayer\",\n            name: \"Technical Layer\",\n            nameAr: \"الطبقة التقنية\",\n            emoji: \"⚙️\",\n            data: technicalLayer,\n            href: \"/technical-layer\"\n        },\n        {\n            key: \"legalRisk\",\n            name: \"Legal & Privacy\",\n            nameAr: \"القانونية والخصوصية\",\n            emoji: \"\\uD83D\\uDD12\",\n            data: legalRisk,\n            href: \"/legal-risk\"\n        }\n    ];\n    const generateCompleteOutput = ()=>{\n        const allData = getAllData();\n        if (outputFormat === \"markdown\") {\n            let markdown = `# ContextKit - Complete AI Project Context\\n\\n`;\n            markdown += `Generated on: ${new Date().toLocaleDateString()}\\n\\n`;\n            modules.forEach((module)=>{\n                const title = isArabic ? module.nameAr : module.name;\n                markdown += `## ${module.emoji} ${title}\\n\\n`;\n                Object.entries(module.data).forEach(([key, value])=>{\n                    if (value && typeof value === \"string\" && value.trim()) {\n                        const formattedKey = key.replace(/([A-Z])/g, \" $1\").replace(/^./, (str)=>str.toUpperCase());\n                        markdown += `### ${formattedKey}\\n${value}\\n\\n`;\n                    }\n                });\n            });\n            return markdown;\n        }\n        if (outputFormat === \"json\") {\n            return JSON.stringify({\n                contextKit: {\n                    metadata: {\n                        generatedAt: new Date().toISOString(),\n                        language: currentLanguage,\n                        version: \"1.0\"\n                    },\n                    modules: allData\n                }\n            }, null, 2);\n        }\n        if (outputFormat === \"html\") {\n            let html = `<!DOCTYPE html>\\n<html lang=\"${currentLanguage}\">\\n<head>\\n`;\n            html += `  <meta charset=\"UTF-8\">\\n`;\n            html += `  <title>ContextKit - AI Project Context</title>\\n`;\n            html += `  <style>body{font-family:Arial,sans-serif;max-width:800px;margin:0 auto;padding:20px;}</style>\\n`;\n            html += `</head>\\n<body>\\n`;\n            html += `  <h1>🧠 ContextKit - Complete AI Project Context</h1>\\n`;\n            html += `  <p><em>Generated on: ${new Date().toLocaleDateString()}</em></p>\\n\\n`;\n            modules.forEach((module)=>{\n                const title = isArabic ? module.nameAr : module.name;\n                html += `  <section>\\n    <h2>${module.emoji} ${title}</h2>\\n`;\n                Object.entries(module.data).forEach(([key, value])=>{\n                    if (value && typeof value === \"string\" && value.trim()) {\n                        const formattedKey = key.replace(/([A-Z])/g, \" $1\").replace(/^./, (str)=>str.toUpperCase());\n                        html += `    <h3>${formattedKey}</h3>\\n    <p>${value}</p>\\n`;\n                    }\n                });\n                html += `  </section>\\n\\n`;\n            });\n            html += `</body>\\n</html>`;\n            return html;\n        }\n        return \"\";\n    };\n    const handleCopyAll = async ()=>{\n        const output = generateCompleteOutput();\n        await navigator.clipboard.writeText(output);\n        setCopied(true);\n        setTimeout(()=>setCopied(false), 2000);\n    };\n    const handleDownload = ()=>{\n        const output = generateCompleteOutput();\n        const extension = outputFormat === \"json\" ? \"json\" : outputFormat === \"html\" ? \"html\" : \"md\";\n        const filename = `contextkit-complete.${extension}`;\n        const blob = new Blob([\n            output\n        ], {\n            type: \"text/plain\"\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = filename;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n    };\n    const hasAnyData = modules.some((module)=>Object.values(module.data).some((value)=>value && typeof value === \"string\" && value.trim()));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-purple-50 to-pink-100 dark:from-gray-900 dark:to-gray-800\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    title: isArabic ? \"المعاينة النهائية\" : \"Final Preview\",\n                    subtitle: isArabic ? \"راجع واستخرج السياق الكامل لمشروعك\" : \"Review and export your complete project context\",\n                    emoji: \"\\uD83D\\uDCCB\",\n                    backLink: {\n                        href: \"/emotional-tone\",\n                        label: isArabic ? \"← العودة للنبرة العاطفية\" : \"← Back to Emotional Tone\"\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-900 dark:text-white mb-6 text-center\",\n                            children: isArabic ? \"نظرة عامة على المحاور\" : \"Modules Overview\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: modules.map((module)=>{\n                                const hasData = Object.values(module.data).some((value)=>value && typeof value === \"string\" && value.trim());\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-2xl mr-3\",\n                                                            children: module.emoji\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                            children: isArabic ? module.nameAr : module.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                            lineNumber: 190,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `w-3 h-3 rounded-full ${hasData ? \"bg-green-500\" : \"bg-gray-300\"}`\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400 mb-4\",\n                                            children: hasData ? isArabic ? \"مكتمل\" : \"Completed\" : isArabic ? \"غير مكتمل\" : \"Incomplete\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: module.href,\n                                            className: \"text-blue-600 dark:text-blue-400 hover:underline text-sm\",\n                                            children: [\n                                                isArabic ? \"تعديل\" : \"Edit\",\n                                                \" →\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, module.key, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 9\n                }, this),\n                hasAnyData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900 dark:text-white mb-6\",\n                                children: isArabic ? \"تصدير السياق الكامل\" : \"Export Complete Context\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: [\n                                            \"markdown\",\n                                            \"html\",\n                                            \"json\"\n                                        ].map((format)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setOutputFormat(format),\n                                                className: `px-4 py-2 rounded-lg transition-colors ${outputFormat === format ? \"bg-blue-600 text-white\" : \"bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600\"}`,\n                                                children: format.toUpperCase()\n                                            }, format, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleCopyAll,\n                                                className: \"flex items-center px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-2\",\n                                                        children: \"\\uD83D\\uDCCB\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    copied ? isArabic ? \"تم النسخ!\" : \"Copied!\" : isArabic ? \"نسخ الكل\" : \"Copy All\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleDownload,\n                                                className: \"flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-2\",\n                                                        children: \"\\uD83D\\uDCBE\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    isArabic ? \"تحميل\" : \"Download\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 dark:bg-gray-900 rounded-lg p-6 max-h-96 overflow-y-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                    className: `text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap font-mono ${isArabic ? \"text-right\" : \"text-left\"}`,\n                                    children: generateCompleteOutput()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                    lineNumber: 218,\n                    columnNumber: 11\n                }, this),\n                !hasAnyData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-2xl mx-auto text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-6xl mb-4 block\",\n                                children: \"\\uD83D\\uDCDD\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900 dark:text-white mb-4\",\n                                children: isArabic ? \"لا توجد بيانات للمعاينة\" : \"No Data to Preview\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 dark:text-gray-400 mb-6\",\n                                children: isArabic ? \"ابدأ بملء المحاور المختلفة لرؤية المعاينة النهائية\" : \"Start filling out the different modules to see the final preview\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/project-definition\",\n                                className: \"inline-block px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors\",\n                                children: isArabic ? \"ابدأ من تعريف المشروع\" : \"Start with Project Definition\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n            lineNumber: 162,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\final-preview\\\\page.tsx\",\n        lineNumber: 161,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/final-preview/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _ThemeToggle__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ThemeToggle */ \"(ssr)/./src/components/ThemeToggle.tsx\");\n/* harmony import */ var _LanguageToggle__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./LanguageToggle */ \"(ssr)/./src/components/LanguageToggle.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Header({ title, subtitle, backLink, emoji }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-4 right-4 z-50 flex space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-lg shadow-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LanguageToggle__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-lg shadow-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ThemeToggle__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-8 pt-4\",\n                children: [\n                    backLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        href: backLink.href,\n                        className: \"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mb-4 inline-block transition-colors\",\n                        children: backLink.label\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-2\",\n                        children: [\n                            emoji && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-2\",\n                                children: emoji\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 21\n                            }, this),\n                            title\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto\",\n                        children: subtitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/LanguageToggle.tsx":
/*!*******************************************!*\
  !*** ./src/components/LanguageToggle.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LanguageToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction LanguageToggle() {\n    const { currentLanguage, setLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_1__.useContextStore)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: ()=>setLanguage(currentLanguage === \"ar\" ? \"en\" : \"ar\"),\n        className: \"flex items-center px-3 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors text-sm font-medium\",\n        title: currentLanguage === \"ar\" ? \"Switch to English\" : \"التبديل إلى العربية\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"mr-2\",\n                children: \"\\uD83C\\uDF10\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            currentLanguage === \"ar\" ? \"EN\" : \"عر\"\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9MYW5ndWFnZVRvZ2dsZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFFdUQ7QUFFeEMsU0FBU0M7SUFDdEIsTUFBTSxFQUFFQyxlQUFlLEVBQUVDLFdBQVcsRUFBRSxHQUFHSCxvRUFBZUE7SUFFeEQscUJBQ0UsOERBQUNJO1FBQ0NDLFNBQVMsSUFBTUYsWUFBWUQsb0JBQW9CLE9BQU8sT0FBTztRQUM3REksV0FBVTtRQUNWQyxPQUFPTCxvQkFBb0IsT0FBTyxzQkFBc0I7OzBCQUV4RCw4REFBQ007Z0JBQUtGLFdBQVU7MEJBQU87Ozs7OztZQUN0Qkosb0JBQW9CLE9BQU8sT0FBTzs7Ozs7OztBQUd6QyIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbnRleHRraXQvLi9zcmMvY29tcG9uZW50cy9MYW5ndWFnZVRvZ2dsZS50c3g/MTM4MCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZUNvbnRleHRTdG9yZSB9IGZyb20gJ0Avc3RvcmUvY29udGV4dFN0b3JlJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTGFuZ3VhZ2VUb2dnbGUoKSB7XG4gIGNvbnN0IHsgY3VycmVudExhbmd1YWdlLCBzZXRMYW5ndWFnZSB9ID0gdXNlQ29udGV4dFN0b3JlKCk7XG5cbiAgcmV0dXJuIChcbiAgICA8YnV0dG9uXG4gICAgICBvbkNsaWNrPXsoKSA9PiBzZXRMYW5ndWFnZShjdXJyZW50TGFuZ3VhZ2UgPT09ICdhcicgPyAnZW4nIDogJ2FyJyl9XG4gICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBweC0zIHB5LTIgYmctZ3JheS0xMDAgZGFyazpiZy1ncmF5LTcwMCBob3ZlcjpiZy1ncmF5LTIwMCBkYXJrOmhvdmVyOmJnLWdyYXktNjAwIHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1jb2xvcnMgdGV4dC1zbSBmb250LW1lZGl1bVwiXG4gICAgICB0aXRsZT17Y3VycmVudExhbmd1YWdlID09PSAnYXInID8gJ1N3aXRjaCB0byBFbmdsaXNoJyA6ICfYp9mE2KrYqNiv2YrZhCDYpdmE2Ykg2KfZhNi52LHYqNmK2KknfVxuICAgID5cbiAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1yLTJcIj7wn4yQPC9zcGFuPlxuICAgICAge2N1cnJlbnRMYW5ndWFnZSA9PT0gJ2FyJyA/ICdFTicgOiAn2LnYsSd9XG4gICAgPC9idXR0b24+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlQ29udGV4dFN0b3JlIiwiTGFuZ3VhZ2VUb2dnbGUiLCJjdXJyZW50TGFuZ3VhZ2UiLCJzZXRMYW5ndWFnZSIsImJ1dHRvbiIsIm9uQ2xpY2siLCJjbGFzc05hbWUiLCJ0aXRsZSIsInNwYW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/LanguageToggle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ThemeProvider.tsx":
/*!******************************************!*\
  !*** ./src/components/ThemeProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme auto */ \n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ThemeProvider({ children }) {\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"light\");\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize theme from localStorage or system preference\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedTheme = localStorage.getItem(\"theme\");\n        const systemTheme = window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\";\n        const initialTheme = savedTheme || systemTheme;\n        setTheme(initialTheme);\n        setMounted(true);\n    }, []);\n    // Apply theme to document\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (mounted) {\n            const root = document.documentElement;\n            if (theme === \"dark\") {\n                root.classList.add(\"dark\");\n            } else {\n                root.classList.remove(\"dark\");\n            }\n            localStorage.setItem(\"theme\", theme);\n        }\n    }, [\n        theme,\n        mounted\n    ]);\n    const toggleTheme = ()=>{\n        setTheme((prev)=>prev === \"light\" ? \"dark\" : \"light\");\n    };\n    // Prevent hydration mismatch\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: {\n            theme,\n            toggleTheme\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeProvider.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        // Return default values instead of throwing error during SSR\n        return {\n            theme: \"light\",\n            toggleTheme: ()=>{}\n        };\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ThemeProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ThemeToggle.tsx":
/*!****************************************!*\
  !*** ./src/components/ThemeToggle.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ThemeToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _ThemeProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ThemeProvider */ \"(ssr)/./src/components/ThemeProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ThemeToggle() {\n    const { theme, toggleTheme } = (0,_ThemeProvider__WEBPACK_IMPORTED_MODULE_1__.useTheme)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: toggleTheme,\n        className: \"p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200 hover:scale-105 active:scale-95\",\n        \"aria-label\": \"Toggle theme\",\n        title: `Switch to ${theme === \"light\" ? \"dark\" : \"light\"} mode`,\n        children: theme === \"light\" ? // Moon icon for dark mode\n        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6 text-gray-600 dark:text-gray-400 transition-colors\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 24,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n            lineNumber: 17,\n            columnNumber: 9\n        }, this) : // Sun icon for light mode\n        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6 text-gray-600 dark:text-gray-400 transition-colors\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 40,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n            lineNumber: 33,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ThemeToggle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/store/contextStore.ts":
/*!***********************************!*\
  !*** ./src/store/contextStore.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useContextStore: () => (/* binding */ useContextStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n\n\n// القيم الافتراضية\nconst initialState = {\n    projectDefinition: {\n        name: \"\",\n        purpose: \"\",\n        targetUsers: \"\",\n        goals: \"\",\n        scope: \"\",\n        timeline: \"\"\n    },\n    contextMap: {\n        timeContext: \"\",\n        language: \"\",\n        location: \"\",\n        culturalContext: \"\",\n        behavioralAspects: \"\",\n        environmentalFactors: \"\"\n    },\n    emotionalTone: {\n        personality: \"\",\n        communicationStyle: \"\",\n        userExperience: \"\",\n        brandVoice: \"\",\n        emotionalIntelligence: \"\",\n        interactionFlow: \"\"\n    },\n    technicalLayer: {\n        programmingLanguages: \"\",\n        frameworks: \"\",\n        llmModels: \"\",\n        databases: \"\",\n        apis: \"\",\n        infrastructure: \"\"\n    },\n    legalRisk: {\n        privacyConcerns: \"\",\n        dataProtection: \"\",\n        compliance: \"\",\n        risks: \"\",\n        mitigation: \"\",\n        ethicalConsiderations: \"\"\n    },\n    currentLanguage: \"ar\",\n    outputFormat: \"markdown\"\n};\n// إنشاء المتجر مع التخزين المستمر\nconst useContextStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        ...initialState,\n        updateProjectDefinition: (data)=>set((state)=>({\n                    projectDefinition: {\n                        ...state.projectDefinition,\n                        ...data\n                    }\n                })),\n        updateContextMap: (data)=>set((state)=>({\n                    contextMap: {\n                        ...state.contextMap,\n                        ...data\n                    }\n                })),\n        updateEmotionalTone: (data)=>set((state)=>({\n                    emotionalTone: {\n                        ...state.emotionalTone,\n                        ...data\n                    }\n                })),\n        updateTechnicalLayer: (data)=>set((state)=>({\n                    technicalLayer: {\n                        ...state.technicalLayer,\n                        ...data\n                    }\n                })),\n        updateLegalRisk: (data)=>set((state)=>({\n                    legalRisk: {\n                        ...state.legalRisk,\n                        ...data\n                    }\n                })),\n        setLanguage: (lang)=>set({\n                currentLanguage: lang\n            }),\n        setOutputFormat: (format)=>set({\n                outputFormat: format\n            }),\n        resetAll: ()=>set(initialState),\n        getModuleData: (module)=>{\n            const state = get();\n            switch(module){\n                case \"project\":\n                    return state.projectDefinition;\n                case \"context\":\n                    return state.contextMap;\n                case \"emotional\":\n                    return state.emotionalTone;\n                case \"technical\":\n                    return state.technicalLayer;\n                case \"legal\":\n                    return state.legalRisk;\n                default:\n                    return {};\n            }\n        },\n        getAllData: ()=>{\n            const state = get();\n            return {\n                projectDefinition: state.projectDefinition,\n                contextMap: state.contextMap,\n                emotionalTone: state.emotionalTone,\n                technicalLayer: state.technicalLayer,\n                legalRisk: state.legalRisk\n            };\n        }\n    }), {\n    name: \"contextkit-storage\",\n    version: 1\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/contextStore.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"384e4943f0bd\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29udGV4dGtpdC8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/N2UzNSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjM4NGU0OTQzZjBiZFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/final-preview/page.tsx":
/*!****************************************!*\
  !*** ./src/app/final-preview/page.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\app\final-preview\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\app\final-preview\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_ThemeProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ThemeProvider */ \"(rsc)/./src/components/ThemeProvider.tsx\");\n\n\n\nconst metadata = {\n    title: \"ContextKit - AI Context Builder\",\n    description: \"Create organized, actionable context for AI-driven projects\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"antialiased\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeProvider__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUN1QjtBQUNvQztBQUVwRCxNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFVO3NCQUNkLDRFQUFDVCxvRUFBYUE7MEJBQ1hLOzs7Ozs7Ozs7Ozs7Ozs7O0FBS1giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb250ZXh0a2l0Ly4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gXCJuZXh0XCI7XG5pbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCI7XG5pbXBvcnQgeyBUaGVtZVByb3ZpZGVyIH0gZnJvbSBcIkAvY29tcG9uZW50cy9UaGVtZVByb3ZpZGVyXCI7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiBcIkNvbnRleHRLaXQgLSBBSSBDb250ZXh0IEJ1aWxkZXJcIixcbiAgZGVzY3JpcHRpb246IFwiQ3JlYXRlIG9yZ2FuaXplZCwgYWN0aW9uYWJsZSBjb250ZXh0IGZvciBBSS1kcml2ZW4gcHJvamVjdHNcIixcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IFJlYWRvbmx5PHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0+KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9XCJhbnRpYWxpYXNlZFwiPlxuICAgICAgICA8VGhlbWVQcm92aWRlcj5cbiAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgIDwvVGhlbWVQcm92aWRlcj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiVGhlbWVQcm92aWRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ThemeProvider.tsx":
/*!******************************************!*\
  !*** ./src/components/ThemeProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0),
/* harmony export */   useTheme: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\ThemeProvider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\ThemeProvider.tsx#ThemeProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\ThemeProvider.tsx#useTheme`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zustand","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffinal-preview%2Fpage&page=%2Ffinal-preview%2Fpage&appPaths=%2Ffinal-preview%2Fpage&pagePath=private-next-app-dir%2Ffinal-preview%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();