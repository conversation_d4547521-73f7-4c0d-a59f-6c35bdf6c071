"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/project-definition/page",{

/***/ "(app-pages-browser)/./src/app/project-definition/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/project-definition/page.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProjectDefinition; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ModuleLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ModuleLayout */ \"(app-pages-browser)/./src/components/ModuleLayout.tsx\");\n/* harmony import */ var _components_SmartQuestion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/SmartQuestion */ \"(app-pages-browser)/./src/components/SmartQuestion.tsx\");\n/* harmony import */ var _components_OutputPanel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/OutputPanel */ \"(app-pages-browser)/./src/components/OutputPanel.tsx\");\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ProjectDefinition() {\n    _s();\n    const { projectDefinition, updateProjectDefinition } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_4__.useContextStore)();\n    const questions = [\n        {\n            id: \"name\",\n            question: \"What is the name of your AI project?\",\n            questionAr: \"ما هو اسم مشروع الذكاء الاصطناعي الخاص بك؟\",\n            placeholder: \"e.g., Smart Customer Support Bot, Content Generator AI, etc.\",\n            placeholderAr: \"مثال: بوت دعم العملاء الذكي، مولد المحتوى بالذكاء الاصطناعي، إلخ.\",\n            type: \"text\",\n            aiSuggestion: \"Choose a clear, descriptive name that reflects your project's main function and target audience.\",\n            aiSuggestionAr: \"اختر اسماً واضحاً ووصفياً يعكس الوظيفة الرئيسية لمشروعك والجمهور المستهدف.\",\n            promptTemplate: 'Help me refine this AI project name: \"{answer}\". Suggest improvements for clarity and market appeal.'\n        },\n        {\n            id: \"purpose\",\n            question: \"What is the main purpose or problem your AI project aims to solve?\",\n            questionAr: \"ما هو الهدف الرئيسي أو المشكلة التي يهدف مشروع الذكاء الاصطناعي لحلها؟\",\n            placeholder: \"Describe the core problem you want to address...\",\n            placeholderAr: \"صف المشكلة الأساسية التي تريد معالجتها...\",\n            aiSuggestion: \"Focus on a specific, measurable problem. Avoid being too broad or vague.\",\n            aiSuggestionAr: \"ركز على مشكلة محددة وقابلة للقياس. تجنب أن تكون عاماً أو غامضاً.\",\n            promptTemplate: 'Analyze this problem statement for an AI project: \"{answer}\". Help me make it more specific and actionable.'\n        },\n        {\n            id: \"targetUsers\",\n            question: \"Who are the primary users or beneficiaries of this project?\",\n            questionAr: \"من هم المستخدمون الأساسيون أو المستفيدون من هذا المشروع؟\",\n            placeholder: \"e.g., Customer service teams, Content creators, Students, etc.\",\n            placeholderAr: \"مثال: فرق خدمة العملاء، منشئو المحتوى، الطلاب، إلخ.\",\n            aiSuggestion: \"Be specific about user demographics, roles, and their current pain points.\",\n            aiSuggestionAr: \"كن محدداً حول التركيبة السكانية للمستخدمين وأدوارهم ونقاط الألم الحالية لديهم.\",\n            promptTemplate: 'Help me create detailed user personas for this target audience: \"{answer}\". Include their needs and challenges.'\n        }\n    ];\n    const handleFieldChange = (field, value)=>{\n        updateProjectDefinition({\n            [field]: value\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ModuleLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        title: \"Project Definition\",\n        titleAr: \"تعريف المشروع\",\n        subtitle: \"Define the scope, users, and goals of your AI project\",\n        subtitleAr: \"حدد نطاق مشروعك والمستخدمين والأهداف\",\n        emoji: \"\\uD83C\\uDFAF\",\n        moduleKey: \"project-definition\",\n        backLink: {\n            href: \"/\",\n            label: \"← Back to Home\",\n            labelAr: \"← العودة للرئيسية\"\n        },\n        nextLink: {\n            href: \"/context-map\",\n            label: \"Next: Context Map →\",\n            labelAr: \"التالي: خريطة السياق ←\"\n        },\n        rightPanel: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OutputPanel__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            moduleData: projectDefinition,\n            moduleName: \"Project Definition\",\n            moduleNameAr: \"تعريف المشروع\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\project-definition\\\\page.tsx\",\n            lineNumber: 68,\n            columnNumber: 9\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: questions.map((question)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SmartQuestion__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    id: question.id,\n                    question: question.question,\n                    questionAr: question.questionAr,\n                    placeholder: question.placeholder,\n                    placeholderAr: question.placeholderAr,\n                    value: projectDefinition[question.id] || \"\",\n                    onChange: (value)=>handleFieldChange(question.id, value),\n                    type: question.type,\n                    aiSuggestion: question.aiSuggestion,\n                    aiSuggestionAr: question.aiSuggestionAr,\n                    promptTemplate: question.promptTemplate\n                }, question.id, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\project-definition\\\\page.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\project-definition\\\\page.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\project-definition\\\\page.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectDefinition, \"b1u7zlnbPXM3lhftuhCjgzVu/Sk=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_4__.useContextStore\n    ];\n});\n_c = ProjectDefinition;\nvar _c;\n$RefreshReg$(_c, \"ProjectDefinition\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/project-definition/page.tsx\n"));

/***/ })

});