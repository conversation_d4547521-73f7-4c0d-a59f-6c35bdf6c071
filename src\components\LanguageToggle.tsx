'use client';

import { useContextStore } from '@/store/contextStore';

export default function LanguageToggle() {
  const { currentLanguage, setLanguage } = useContextStore();

  return (
    <button
      onClick={() => setLanguage(currentLanguage === 'ar' ? 'en' : 'ar')}
      className="flex items-center px-3 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors text-sm font-medium"
      title={currentLanguage === 'ar' ? 'Switch to English' : 'التبديل إلى العربية'}
    >
      <span className="mr-2">🌐</span>
      {currentLanguage === 'ar' ? 'EN' : 'عر'}
    </button>
  );
}
