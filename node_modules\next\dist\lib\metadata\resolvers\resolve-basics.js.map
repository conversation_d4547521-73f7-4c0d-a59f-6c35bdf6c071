{"version": 3, "sources": ["../../../../src/lib/metadata/resolvers/resolve-basics.ts"], "names": ["resolveAlternates", "resolveAppLinks", "resolveAppleWebApp", "resolveItunes", "resolveRobots", "resolveThemeColor", "resolveVerification", "resolveAlternateUrl", "url", "metadataBase", "metadataContext", "URL", "pathname", "resolveAbsoluteUrlWithPathname", "themeColor", "resolveAsArrayOrUndefined", "themeColorDescriptors", "for<PERSON>ach", "descriptor", "push", "color", "media", "resolveUrlValuesOfObject", "obj", "result", "key", "value", "Object", "entries", "item", "index", "title", "resolveCanonicalUrl", "urlOrDescriptor", "alternates", "context", "canonical", "languages", "types", "robotsKeys", "resolveRobotsValue", "robots", "values", "follow", "join", "basic", "googleBot", "VerificationKeys", "verification", "res", "other", "otherKey", "otherValue", "appWebApp", "capable", "startupImages", "startupImage", "map", "statusBarStyle", "appLinks", "itunes", "appId", "appArgument", "undefined"], "mappings": ";;;;;;;;;;;;;;;;;;;;IAuGaA,iBAAiB;eAAjBA;;IAiIAC,eAAe;eAAfA;;IAtBAC,kBAAkB;eAAlBA;;IA+BAC,aAAa;eAAbA;;IAhEAC,aAAa;eAAbA;;IAlJAC,iBAAiB;eAAjBA;;IA4JAC,mBAAmB;eAAnBA;;;uBA5K6B;4BACK;AAE/C,SAASC,oBACPC,GAAiB,EACjBC,YAAwB,EACxBC,eAAgC;IAEhC,0CAA0C;IAC1C,8DAA8D;IAC9D,IAAIF,eAAeG,KAAK;QACtBH,MAAM,IAAIG,IAAID,gBAAgBE,QAAQ,EAAEJ;IAC1C;IACA,OAAOK,IAAAA,0CAA8B,EAACL,KAAKC,cAAcC;AAC3D;AAEO,MAAML,oBAA2D,CACtES;QAKAC;IAHA,IAAI,CAACD,YAAY,OAAO;IACxB,MAAME,wBAAgD,EAAE;KAExDD,6BAAAA,IAAAA,gCAAyB,EAACD,gCAA1BC,2BAAuCE,OAAO,CAAC,CAACC;QAC9C,IAAI,OAAOA,eAAe,UACxBF,sBAAsBG,IAAI,CAAC;YAAEC,OAAOF;QAAW;aAC5C,IAAI,OAAOA,eAAe,UAC7BF,sBAAsBG,IAAI,CAAC;YACzBC,OAAOF,WAAWE,KAAK;YACvBC,OAAOH,WAAWG,KAAK;QACzB;IACJ;IAEA,OAAOL;AACT;AAEA,SAASM,yBACPC,GAMa,EACbd,YAA8C,EAC9CC,eAAgC;IAEhC,IAAI,CAACa,KAAK,OAAO;IAEjB,MAAMC,SAAoD,CAAC;IAC3D,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACL,KAAM;QAC9C,IAAI,OAAOG,UAAU,YAAYA,iBAAiBf,KAAK;YACrDa,MAAM,CAACC,IAAI,GAAG;gBACZ;oBACEjB,KAAKD,oBAAoBmB,OAAOjB,cAAcC;gBAChD;aACD;QACH,OAAO;YACLc,MAAM,CAACC,IAAI,GAAG,EAAE;YAChBC,yBAAAA,MAAOT,OAAO,CAAC,CAACY,MAAMC;gBACpB,MAAMtB,MAAMD,oBAAoBsB,KAAKrB,GAAG,EAAEC,cAAcC;gBACxDc,MAAM,CAACC,IAAI,CAACK,MAAM,GAAG;oBACnBtB;oBACAuB,OAAOF,KAAKE,KAAK;gBACnB;YACF;QACF;IACF;IACA,OAAOP;AACT;AAEA,SAASQ,oBACPC,eAA0E,EAC1ExB,YAAwB,EACxBC,eAAgC;IAEhC,IAAI,CAACuB,iBAAiB,OAAO;IAE7B,MAAMzB,MACJ,OAAOyB,oBAAoB,YAAYA,2BAA2BtB,MAC9DsB,kBACAA,gBAAgBzB,GAAG;IAEzB,qEAAqE;IACrE,OAAO;QACLA,KAAKD,oBAAoBC,KAAKC,cAAcC;IAC9C;AACF;AAEO,MAAMV,oBAGT,CAACkC,YAAYzB,cAAc0B;IAC7B,IAAI,CAACD,YAAY,OAAO;IAExB,MAAME,YAAYJ,oBAChBE,WAAWE,SAAS,EACpB3B,cACA0B;IAEF,MAAME,YAAYf,yBAChBY,WAAWG,SAAS,EACpB5B,cACA0B;IAEF,MAAMd,QAAQC,yBACZY,WAAWb,KAAK,EAChBZ,cACA0B;IAEF,MAAMG,QAAQhB,yBACZY,WAAWI,KAAK,EAChB7B,cACA0B;IAGF,MAAMX,SAAgC;QACpCY;QACAC;QACAhB;QACAiB;IACF;IAEA,OAAOd;AACT;AAEA,MAAMe,aAAa;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AACD,MAAMC,qBAAoE,CACxEC;IAEA,IAAI,CAACA,QAAQ,OAAO;IACpB,IAAI,OAAOA,WAAW,UAAU,OAAOA;IAEvC,MAAMC,SAAmB,EAAE;IAE3B,IAAID,OAAOX,KAAK,EAAEY,OAAOvB,IAAI,CAAC;SACzB,IAAI,OAAOsB,OAAOX,KAAK,KAAK,WAAWY,OAAOvB,IAAI,CAAC;IAExD,IAAIsB,OAAOE,MAAM,EAAED,OAAOvB,IAAI,CAAC;SAC1B,IAAI,OAAOsB,OAAOE,MAAM,KAAK,WAAWD,OAAOvB,IAAI,CAAC;IAEzD,KAAK,MAAMM,OAAOc,WAAY;QAC5B,MAAMb,QAAQe,MAAM,CAAChB,IAAI;QACzB,IAAI,OAAOC,UAAU,eAAeA,UAAU,OAAO;YACnDgB,OAAOvB,IAAI,CAAC,OAAOO,UAAU,YAAYD,MAAM,CAAC,EAAEA,IAAI,CAAC,EAAEC,MAAM,CAAC;QAClE;IACF;IAEA,OAAOgB,OAAOE,IAAI,CAAC;AACrB;AAEO,MAAMxC,gBAAyC,CAACqC;IACrD,IAAI,CAACA,QAAQ,OAAO;IACpB,OAAO;QACLI,OAAOL,mBAAmBC;QAC1BK,WACE,OAAOL,WAAW,WAAWD,mBAAmBC,OAAOK,SAAS,IAAI;IACxE;AACF;AAEA,MAAMC,mBAAmB;IAAC;IAAU;IAAS;IAAU;IAAM;CAAQ;AAC9D,MAAMzC,sBAAqD,CAChE0C;IAEA,IAAI,CAACA,cAAc,OAAO;IAC1B,MAAMC,MAA4B,CAAC;IAEnC,KAAK,MAAMxB,OAAOsB,iBAAkB;QAClC,MAAMrB,QAAQsB,YAAY,CAACvB,IAAI;QAC/B,IAAIC,OAAO;YACT,IAAID,QAAQ,SAAS;gBACnBwB,IAAIC,KAAK,GAAG,CAAC;gBACb,IAAK,MAAMC,YAAYH,aAAaE,KAAK,CAAE;oBACzC,MAAME,aAAarC,IAAAA,gCAAyB,EAC1CiC,aAAaE,KAAK,CAACC,SAAS;oBAE9B,IAAIC,YAAYH,IAAIC,KAAK,CAACC,SAAS,GAAGC;gBACxC;YACF,OAAOH,GAAG,CAACxB,IAAI,GAAGV,IAAAA,gCAAyB,EAACW;QAC9C;IACF;IACA,OAAOuB;AACT;AAEO,MAAM/C,qBAAmD,CAACmD;QAS3DtC;IARJ,IAAI,CAACsC,WAAW,OAAO;IACvB,IAAIA,cAAc,MAAM;QACtB,OAAO;YACLC,SAAS;QACX;IACF;IAEA,MAAMC,gBAAgBF,UAAUG,YAAY,IACxCzC,6BAAAA,IAAAA,gCAAyB,EAACsC,UAAUG,YAAY,sBAAhDzC,2BAAmD0C,GAAG,CAAC,CAAC5B,OACtD,OAAOA,SAAS,WAAW;YAAErB,KAAKqB;QAAK,IAAIA,QAE7C;IAEJ,OAAO;QACLyB,SAAS,aAAaD,YAAY,CAAC,CAACA,UAAUC,OAAO,GAAG;QACxDvB,OAAOsB,UAAUtB,KAAK,IAAI;QAC1ByB,cAAcD;QACdG,gBAAgBL,UAAUK,cAAc,IAAI;IAC9C;AACF;AAEO,MAAMzD,kBAA6C,CAAC0D;IACzD,IAAI,CAACA,UAAU,OAAO;IACtB,IAAK,MAAMlC,OAAOkC,SAAU;QAC1B,iCAAiC;QACjCA,QAAQ,CAAClC,IAAI,GAAGV,IAAAA,gCAAyB,EAAC4C,QAAQ,CAAClC,IAAI;IACzD;IACA,OAAOkC;AACT;AAEO,MAAMxD,gBAGT,CAACyD,QAAQnD,cAAc0B;IACzB,IAAI,CAACyB,QAAQ,OAAO;IACpB,OAAO;QACLC,OAAOD,OAAOC,KAAK;QACnBC,aAAaF,OAAOE,WAAW,GAC3BvD,oBAAoBqD,OAAOE,WAAW,EAAErD,cAAc0B,WACtD4B;IACN;AACF"}