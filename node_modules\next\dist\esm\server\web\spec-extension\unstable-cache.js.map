{"version": 3, "sources": ["../../../../src/server/web/spec-extension/unstable-cache.ts"], "names": ["CACHE_ONE_YEAR", "addImplicitTags", "validateRevalidate", "validateTags", "staticGenerationAsyncStorage", "noStoreFetchIdx", "cacheNewResult", "result", "incrementalCache", "cache<PERSON>ey", "tags", "revalidate", "fetchIdx", "fetchUrl", "set", "kind", "data", "headers", "body", "JSON", "stringify", "status", "url", "fetchCache", "unstable_cache", "cb", "keyParts", "options", "Error", "toString", "name", "fixedKey", "Array", "isArray", "join", "cachedCb", "args", "store", "getStore", "maybeIncrementalCache", "globalThis", "__incrementalCache", "pathname", "searchParams", "URL", "urlPathname", "sortedSearchKeys", "keys", "sort", "a", "b", "localeCompare", "sortedSearch", "map", "key", "get", "invocation<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "nextFetchId", "slice", "tag", "includes", "push", "implicitTags", "isOnDemandRevalidate", "isDraftMode", "cacheEntry", "kindHint", "softTags", "value", "console", "error", "cachedResponse", "undefined", "parse", "isStale", "pendingRevalidates", "run", "isUnstableCacheCallback", "then", "catch", "err", "isStaticGeneration", "prerenderState"], "mappings": "AAEA,SAASA,cAAc,QAAQ,yBAAwB;AACvD,SACEC,eAAe,EACfC,kBAAkB,EAClBC,YAAY,QACP,wBAAuB;AAC9B,SAASC,4BAA4B,QAAQ,sEAAqE;AAIlH,IAAIC,kBAAkB;AAEtB,eAAeC,eACbC,MAAS,EACTC,gBAAkC,EAClCC,QAAgB,EAChBC,IAAc,EACdC,UAAsC,EACtCC,QAAgB,EAChBC,QAAgB;IAEhB,MAAML,iBAAiBM,GAAG,CACxBL,UACA;QACEM,MAAM;QACNC,MAAM;YACJC,SAAS,CAAC;YACV,gCAAgC;YAChCC,MAAMC,KAAKC,SAAS,CAACb;YACrBc,QAAQ;YACRC,KAAK;QACP;QACAX,YAAY,OAAOA,eAAe,WAAWX,iBAAiBW;IAChE,GACA;QACEA;QACAY,YAAY;QACZb;QACAE;QACAC;IACF;IAEF;AACF;AAEA;;;;CAIC,GACD,OAAO,SAASW,eACdC,EAAK,EACLC,QAAmB,EACnBC,UAMI,CAAC,CAAC;IAEN,IAAIA,QAAQhB,UAAU,KAAK,GAAG;QAC5B,MAAM,IAAIiB,MACR,CAAC,wFAAwF,EAAEH,GAAGI,QAAQ,GAAG,CAAC;IAE9G;IAEA,uCAAuC;IACvC,MAAMnB,OAAOiB,QAAQjB,IAAI,GACrBP,aAAawB,QAAQjB,IAAI,EAAE,CAAC,eAAe,EAAEe,GAAGI,QAAQ,GAAG,CAAC,IAC5D,EAAE;IAEN,kCAAkC;IAClC3B,mBACEyB,QAAQhB,UAAU,EAClB,CAAC,eAAe,EAAEc,GAAGK,IAAI,IAAIL,GAAGI,QAAQ,GAAG,CAAC;IAG9C,wFAAwF;IACxF,wDAAwD;IACxD,mDAAmD;IACnD,8DAA8D;IAC9D,8FAA8F;IAC9F,iGAAiG;IACjG,gBAAgB;IAChB,MAAME,WAAW,CAAC,EAAEN,GAAGI,QAAQ,GAAG,CAAC,EACjCG,MAAMC,OAAO,CAACP,aAAaA,SAASQ,IAAI,CAAC,KAC1C,CAAC;IAEF,MAAMC,WAAW,OAAO,GAAGC;QACzB,MAAMC,QAAQjC,6BAA6BkC,QAAQ;QAEnD,mEAAmE;QACnE,MAAMC,wBAGJF,CAAAA,yBAAAA,MAAO7B,gBAAgB,KAAI,AAACgC,WAAmBC,kBAAkB;QAEnE,IAAI,CAACF,uBAAuB;YAC1B,MAAM,IAAIX,MACR,CAAC,sDAAsD,EAAEH,GAAGI,QAAQ,GAAG,CAAC;QAE5E;QACA,MAAMrB,mBAAmB+B;QAEzB,MAAM,EAAEG,QAAQ,EAAEC,YAAY,EAAE,GAAG,IAAIC,IACrCP,CAAAA,yBAAAA,MAAOQ,WAAW,KAAI,KACtB;QAEF,MAAMC,mBAAmB;eAAIH,aAAaI,IAAI;SAAG,CAACC,IAAI,CAAC,CAACC,GAAGC;YACzD,OAAOD,EAAEE,aAAa,CAACD;QACzB;QACA,MAAME,eAAeN,iBAClBO,GAAG,CAAC,CAACC,MAAQ,CAAC,EAAEA,IAAI,CAAC,EAAEX,aAAaY,GAAG,CAACD,KAAK,CAAC,EAC9CpB,IAAI,CAAC;QAER,gEAAgE;QAChE,4FAA4F;QAC5F,gDAAgD;QAChD,MAAMsB,gBAAgB,CAAC,EAAEzB,SAAS,CAAC,EAAEZ,KAAKC,SAAS,CAACgB,MAAM,CAAC;QAC3D,MAAM3B,WAAW,MAAMD,iBAAiBiD,aAAa,CAACD;QACtD,4DAA4D;QAC5D,MAAM3C,WAAW,CAAC,eAAe,EAAE6B,SAAS,EAC1CU,aAAaM,MAAM,GAAG,MAAM,GAC7B,EAAEN,aAAa,CAAC,EAAE3B,GAAGK,IAAI,GAAG,CAAC,CAAC,EAAEL,GAAGK,IAAI,CAAC,CAAC,GAAGrB,SAAS,CAAC;QACvD,MAAMG,WAAW,AAACyB,CAAAA,QAAQA,MAAMsB,WAAW,GAAGtD,eAAc,KAAM;QAElE,IAAIgC,OAAO;YACTA,MAAMsB,WAAW,GAAG/C,WAAW;YAE/B,+FAA+F;YAC/F,qGAAqG;YACrG,4FAA4F;YAE5F,4FAA4F;YAC5F,IAAI,OAAOe,QAAQhB,UAAU,KAAK,UAAU;gBAC1C,IACE,OAAO0B,MAAM1B,UAAU,KAAK,YAC5B0B,MAAM1B,UAAU,GAAGgB,QAAQhB,UAAU,EACrC;gBACA,+EAA+E;gBACjF,OAAO;oBACL0B,MAAM1B,UAAU,GAAGgB,QAAQhB,UAAU;gBACvC;YACF,OAAO,IACLgB,QAAQhB,UAAU,KAAK,SACvB,OAAO0B,MAAM1B,UAAU,KAAK,aAC5B;gBACA,2EAA2E;gBAC3E0B,MAAM1B,UAAU,GAAGgB,QAAQhB,UAAU;YACvC;YAEA,sEAAsE;YACtE,IAAI,CAAC0B,MAAM3B,IAAI,EAAE;gBACf2B,MAAM3B,IAAI,GAAGA,KAAKkD,KAAK;YACzB,OAAO;gBACL,KAAK,MAAMC,OAAOnD,KAAM;oBACtB,4DAA4D;oBAC5D,IAAI,CAAC2B,MAAM3B,IAAI,CAACoD,QAAQ,CAACD,MAAM;wBAC7BxB,MAAM3B,IAAI,CAACqD,IAAI,CAACF;oBAClB;gBACF;YACF;YACA,uGAAuG;YACvG,qDAAqD;YACrD,MAAMG,eAAe/D,gBAAgBoC;YAErC,IACE,sDAAsD;YACtD,4CAA4C;YAC5CA,MAAMd,UAAU,KAAK,oBACrB,CAACc,MAAM4B,oBAAoB,IAC3B,CAACzD,iBAAiByD,oBAAoB,IACtC,CAAC5B,MAAM6B,WAAW,EAClB;gBACA,wEAAwE;gBACxE,MAAMC,aAAa,MAAM3D,iBAAiB+C,GAAG,CAAC9C,UAAU;oBACtD2D,UAAU;oBACVzD,YAAYgB,QAAQhB,UAAU;oBAC9BD;oBACA2D,UAAUL;oBACVpD;oBACAC;gBACF;gBAEA,IAAIsD,cAAcA,WAAWG,KAAK,EAAE;oBAClC,mCAAmC;oBACnC,IAAIH,WAAWG,KAAK,CAACvD,IAAI,KAAK,SAAS;wBACrC,qDAAqD;wBACrD,6FAA6F;wBAC7F,0BAA0B;wBAC1B,+FAA+F;wBAC/FwD,QAAQC,KAAK,CACX,CAAC,0CAA0C,EAAEhB,cAAc,CAAC;oBAE9D,0DAA0D;oBAC5D,OAAO;wBACL,0FAA0F;wBAC1F,0DAA0D;wBAC1D,MAAMiB,iBACJN,WAAWG,KAAK,CAACtD,IAAI,CAACE,IAAI,KAAKwD,YAC3BvD,KAAKwD,KAAK,CAACR,WAAWG,KAAK,CAACtD,IAAI,CAACE,IAAI,IACrCwD;wBACN,IAAIP,WAAWS,OAAO,EAAE;4BACtB,4EAA4E;4BAC5E,IAAI,CAACvC,MAAMwC,kBAAkB,EAAE;gCAC7BxC,MAAMwC,kBAAkB,GAAG,CAAC;4BAC9B;4BACA,iFAAiF;4BACjFxC,MAAMwC,kBAAkB,CAACrB,cAAc,GACrCpD,6BACG0E,GAAG,CACF;gCACE,GAAGzC,KAAK;gCACR,8DAA8D;gCAC9D,8CAA8C;gCAC9Cd,YAAY;gCACZwD,yBAAyB;4BAC3B,GACAtD,OACGW,MAEJ4C,IAAI,CAAC,CAACzE;gCACL,OAAOD,eACLC,QACAC,kBACAC,UACAC,MACAiB,QAAQhB,UAAU,EAClBC,UACAC;4BAEJ,EACA,+DAA+D;6BAC9DoE,KAAK,CAAC,CAACC,MACNX,QAAQC,KAAK,CACX,CAAC,6BAA6B,EAAEhB,cAAc,CAAC,EAC/C0B;wBAGV;wBACA,kDAAkD;wBAClD,OAAOT;oBACT;gBACF;YACF;YAEA,uFAAuF;YACvF,MAAMlE,SAAS,MAAMH,6BAA6B0E,GAAG,CACnD;gBACE,GAAGzC,KAAK;gBACR,8DAA8D;gBAC9D,8CAA8C;gBAC9Cd,YAAY;gBACZwD,yBAAyB;YAC3B,GACAtD,OACGW;YAEL9B,eACEC,QACAC,kBACAC,UACAC,MACAiB,QAAQhB,UAAU,EAClBC,UACAC;YAEF,OAAON;QACT,OAAO;YACLF,mBAAmB;YACnB,mFAAmF;YACnF,8DAA8D;YAC9D,qGAAqG;YACrG,4FAA4F;YAE5F,IAAI,CAACG,iBAAiByD,oBAAoB,EAAE;gBAC1C,+EAA+E;gBAE/E,uGAAuG;gBACvG,qDAAqD;gBACrD,MAAMD,eAAe3B,SAASpC,gBAAgBoC;gBAE9C,MAAM8B,aAAa,MAAM3D,iBAAiB+C,GAAG,CAAC9C,UAAU;oBACtD2D,UAAU;oBACVzD,YAAYgB,QAAQhB,UAAU;oBAC9BD;oBACAE;oBACAC;oBACAwD,UAAUL;gBACZ;gBAEA,IAAIG,cAAcA,WAAWG,KAAK,EAAE;oBAClC,mCAAmC;oBACnC,IAAIH,WAAWG,KAAK,CAACvD,IAAI,KAAK,SAAS;wBACrC,qDAAqD;wBACrD,6FAA6F;wBAC7F,0BAA0B;wBAC1BwD,QAAQC,KAAK,CACX,CAAC,0CAA0C,EAAEhB,cAAc,CAAC;oBAE9D,0DAA0D;oBAC5D,OAAO,IAAI,CAACW,WAAWS,OAAO,EAAE;wBAC9B,8DAA8D;wBAC9D,OAAOT,WAAWG,KAAK,CAACtD,IAAI,CAACE,IAAI,KAAKwD,YAClCvD,KAAKwD,KAAK,CAACR,WAAWG,KAAK,CAACtD,IAAI,CAACE,IAAI,IACrCwD;oBACN;gBACF;YACF;YAEA,uFAAuF;YACvF,8FAA8F;YAC9F,oGAAoG;YACpG,yGAAyG;YACzG,iGAAiG;YACjG,kGAAkG;YAClG,+EAA+E;YAC/E,MAAMnE,SAAS,MAAMH,6BAA6B0E,GAAG,CACnD,uHAAuH;YACvH,0GAA0G;YAC1G,uDAAuD;YACvD;gBACE,8DAA8D;gBAC9D,8CAA8C;gBAC9CvD,YAAY;gBACZwD,yBAAyB;gBACzBlC,aAAa;gBACbsC,oBAAoB;gBACpBC,gBAAgB;YAClB,GACA3D,OACGW;YAEL9B,eACEC,QACAC,kBACAC,UACAC,MACAiB,QAAQhB,UAAU,EAClBC,UACAC;YAEF,OAAON;QACT;IACF;IACA,yGAAyG;IACzG,OAAO4B;AACT"}