"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/context-map/page",{

/***/ "(app-pages-browser)/./src/app/context-map/page.tsx":
/*!**************************************!*\
  !*** ./src/app/context-map/page.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ContextMap; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ModuleLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ModuleLayout */ \"(app-pages-browser)/./src/components/ModuleLayout.tsx\");\n/* harmony import */ var _components_SmartQuestion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/SmartQuestion */ \"(app-pages-browser)/./src/components/SmartQuestion.tsx\");\n/* harmony import */ var _components_OutputPanel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/OutputPanel */ \"(app-pages-browser)/./src/components/OutputPanel.tsx\");\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ContextMap() {\n    _s();\n    const { contextMap, updateContextMap } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_4__.useContextStore)();\n    const questions = [\n        {\n            id: \"timeContext\",\n            question: \"What is the temporal context of your project?\",\n            questionAr: \"ما هو السياق الزمني لمشروعك؟\",\n            placeholder: \"e.g., Global 24/7 support, Business hours EST, Real-time responses...\",\n            placeholderAr: \"مثال: دعم عالمي على مدار الساعة، ساعات العمل بتوقيت شرق أمريكا، استجابات فورية...\",\n            aiSuggestion: \"Consider time zones, working hours, response time expectations, and any time-sensitive requirements.\",\n            aiSuggestionAr: \"فكر في المناطق الزمنية وساعات العمل وتوقعات وقت الاستجابة وأي متطلبات حساسة للوقت.\",\n            promptTemplate: 'Help me optimize this temporal context for an AI project: \"{answer}\". Suggest improvements for better time management.'\n        },\n        {\n            id: \"language\",\n            question: \"What languages should your AI system support?\",\n            questionAr: \"ما هي اللغات التي يجب أن يدعمها نظام الذكاء الاصطناعي؟\",\n            placeholder: \"e.g., English primary, Arabic secondary, Multilingual support...\",\n            placeholderAr: \"مثال: الإنجليزية أساسية، العربية ثانوية، دعم متعدد اللغات...\",\n            type: \"text\",\n            aiSuggestion: \"Consider your target audience, regional requirements, and the complexity of multilingual support.\",\n            aiSuggestionAr: \"فكر في جمهورك المستهدف والمتطلبات الإقليمية وتعقيد الدعم متعدد اللغات.\",\n            promptTemplate: 'Analyze this language requirement for an AI system: \"{answer}\". Suggest implementation strategies.'\n        },\n        {\n            id: \"location\",\n            question: \"What geographic regions or locations will this project serve?\",\n            questionAr: \"ما هي المناطق الجغرافية أو المواقع التي سيخدمها هذا المشروع؟\",\n            placeholder: \"e.g., Middle East, North America, Global, Specific cities...\",\n            placeholderAr: \"مثال: الشرق الأوسط، أمريكا الشمالية، عالمي، مدن محددة...\",\n            aiSuggestion: \"Think about regional regulations, cultural differences, and infrastructure requirements.\",\n            aiSuggestionAr: \"فكر في اللوائح الإقليمية والاختلافات الثقافية ومتطلبات البنية التحتية.\",\n            promptTemplate: 'Help me understand the geographic implications of this scope: \"{answer}\". What should I consider?'\n        }\n    ];\n    const handleFieldChange = (field, value)=>{\n        updateContextMap({\n            [field]: value\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ModuleLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        title: \"Context Map\",\n        titleAr: \"خريطة السياق\",\n        subtitle: \"Define the contextual framework for your AI project\",\n        subtitleAr: \"حدد الإطار السياقي لمشروع الذكاء الاصطناعي\",\n        emoji: \"\\uD83D\\uDDFA️\",\n        moduleKey: \"context-map\",\n        backLink: {\n            href: \"/project-definition\",\n            label: \"← Back to Project Definition\",\n            labelAr: \"← العودة لتعريف المشروع\"\n        },\n        nextLink: {\n            href: \"/emotional-tone\",\n            label: \"Next: Emotional Tone →\",\n            labelAr: \"التالي: النبرة العاطفية ←\"\n        },\n        rightPanel: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OutputPanel__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            moduleData: contextMap,\n            moduleName: \"Context Map\",\n            moduleNameAr: \"خريطة السياق\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n            lineNumber: 68,\n            columnNumber: 9\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: questions.map((question)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SmartQuestion__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    id: question.id,\n                    question: question.question,\n                    questionAr: question.questionAr,\n                    placeholder: question.placeholder,\n                    placeholderAr: question.placeholderAr,\n                    value: contextMap[question.id] || \"\",\n                    onChange: (value)=>handleFieldChange(question.id, value),\n                    type: question.type,\n                    aiSuggestion: question.aiSuggestion,\n                    aiSuggestionAr: question.aiSuggestionAr,\n                    promptTemplate: question.promptTemplate\n                }, question.id, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n_s(ContextMap, \"jlOHj64dV16jvKA34KKvBF1tiHE=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_4__.useContextStore\n    ];\n});\n_c = ContextMap;\nvar _c;\n$RefreshReg$(_c, \"ContextMap\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/context-map/page.tsx\n"));

/***/ })

});