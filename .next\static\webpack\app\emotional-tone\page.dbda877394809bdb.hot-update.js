"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/emotional-tone/page",{

/***/ "(app-pages-browser)/./src/components/OutputPanel.tsx":
/*!****************************************!*\
  !*** ./src/components/OutputPanel.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ OutputPanel; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction OutputPanel(param) {\n    let { moduleData, moduleName, moduleNameAr } = param;\n    _s();\n    const { currentLanguage, outputFormat, setOutputFormat } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore)();\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isArabic = currentLanguage === \"ar\";\n    // تحويل البيانات إلى تنسيقات مختلفة\n    const generateMarkdown = ()=>{\n        const title = isArabic ? moduleNameAr : moduleName;\n        let markdown = \"# \".concat(title, \"\\n\\n\");\n        Object.entries(moduleData).forEach((param)=>{\n            let [key, value] = param;\n            if (value && typeof value === \"string\" && value.trim()) {\n                const formattedKey = key.replace(/([A-Z])/g, \" $1\").replace(/^./, (str)=>str.toUpperCase());\n                markdown += \"## \".concat(formattedKey, \"\\n\").concat(value, \"\\n\\n\");\n            }\n        });\n        return markdown;\n    };\n    const generateHTML = ()=>{\n        const title = isArabic ? moduleNameAr : moduleName;\n        let html = '<div class=\"module-output\">\\n  <h1>'.concat(title, \"</h1>\\n\");\n        Object.entries(moduleData).forEach((param)=>{\n            let [key, value] = param;\n            if (value && typeof value === \"string\" && value.trim()) {\n                const formattedKey = key.replace(/([A-Z])/g, \" $1\").replace(/^./, (str)=>str.toUpperCase());\n                html += \"  <section>\\n    <h2>\".concat(formattedKey, \"</h2>\\n    <p>\").concat(value, \"</p>\\n  </section>\\n\");\n            }\n        });\n        html += \"</div>\";\n        return html;\n    };\n    const generateJSON = ()=>{\n        const filteredData = Object.fromEntries(Object.entries(moduleData).filter((param)=>{\n            let [_, value] = param;\n            return value && typeof value === \"string\" && value.trim();\n        }));\n        return JSON.stringify({\n            module: isArabic ? moduleNameAr : moduleName,\n            data: filteredData,\n            metadata: {\n                timestamp: new Date().toISOString(),\n                language: isArabic ? \"ar\" : \"en\",\n                version: \"1.0\"\n            }\n        }, null, 2);\n    };\n    const generateYAML = ()=>{\n        const filteredData = Object.fromEntries(Object.entries(moduleData).filter((param)=>{\n            let [_, value] = param;\n            return value && typeof value === \"string\" && value.trim();\n        }));\n        let yaml = \"# \".concat(isArabic ? moduleNameAr : moduleName, \"\\n\");\n        yaml += \"# Generated: \".concat(new Date().toISOString(), \"\\n\\n\");\n        Object.entries(filteredData).forEach((param)=>{\n            let [key, value] = param;\n            const formattedKey = key.replace(/([A-Z])/g, \"_$1\").toLowerCase();\n            yaml += \"\".concat(formattedKey, \": |\\n\");\n            const lines = value.split(\"\\n\");\n            lines.forEach((line)=>{\n                yaml += \"  \".concat(line, \"\\n\");\n            });\n            yaml += \"\\n\";\n        });\n        return yaml;\n    };\n    const getCurrentOutput = ()=>{\n        switch(outputFormat){\n            case \"markdown\":\n                return generateMarkdown();\n            case \"html\":\n                return generateHTML();\n            case \"json\":\n                return generateJSON();\n            case \"yaml\":\n                return generateYAML();\n            default:\n                return generateMarkdown();\n        }\n    };\n    const handleCopyAll = async ()=>{\n        const output = getCurrentOutput();\n        await navigator.clipboard.writeText(output);\n        setCopied(true);\n        setTimeout(()=>setCopied(false), 2000);\n    };\n    const handleDownload = ()=>{\n        const output = getCurrentOutput();\n        const extensions = {\n            markdown: \"md\",\n            html: \"html\",\n            json: \"json\",\n            yaml: \"yml\"\n        };\n        const extension = extensions[outputFormat] || \"txt\";\n        const filename = \"\".concat(moduleName.toLowerCase().replace(/\\s+/g, \"-\"), \".\").concat(extension);\n        const mimeTypes = {\n            markdown: \"text/markdown\",\n            html: \"text/html\",\n            json: \"application/json\",\n            yaml: \"text/yaml\"\n        };\n        const mimeType = mimeTypes[outputFormat] || \"text/plain\";\n        const blob = new Blob([\n            output\n        ], {\n            type: mimeType\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = filename;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n    };\n    const hasData = Object.values(moduleData).some((value)=>value && typeof value === \"string\" && value.trim());\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2 flex-wrap gap-2\",\n                        children: [\n                            \"markdown\",\n                            \"html\",\n                            \"json\",\n                            \"yaml\"\n                        ].map((format)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setOutputFormat(format),\n                                className: \"px-3 py-1 text-sm rounded-lg transition-colors \".concat(outputFormat === format ? \"bg-blue-600 text-white\" : \"bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600\"),\n                                children: format.toUpperCase()\n                            }, format, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this),\n                    hasData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleCopyAll,\n                                className: \"flex items-center px-3 py-1 text-sm bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded-lg hover:bg-green-200 dark:hover:bg-green-900/30 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-1\",\n                                        children: \"\\uD83D\\uDCCB\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this),\n                                    copied ? isArabic ? \"تم النسخ!\" : \"Copied!\" : isArabic ? \"نسخ الكل\" : \"Copy All\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleDownload,\n                                className: \"flex items-center px-3 py-1 text-sm bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-900/30 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-1\",\n                                        children: \"\\uD83D\\uDCBE\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, this),\n                                    isArabic ? \"تحميل\" : \"Download\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 dark:bg-gray-900 rounded-lg p-4 min-h-[300px]\",\n                children: hasData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                    className: \"text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap font-mono overflow-x-auto \".concat(isArabic ? \"text-right\" : \"text-left\"),\n                    children: getCurrentOutput()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full text-gray-500 dark:text-gray-400\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-4xl mb-2 block\",\n                                children: \"\\uD83D\\uDCDD\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: isArabic ? \"ابدأ بالإجابة على الأسئلة لرؤية المخرجات\" : \"Start answering questions to see outputs\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                lineNumber: 182,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                children: [\n                    outputFormat === \"markdown\" && (isArabic ? \"تنسيق Markdown - جاهز للاستخدام في المستندات\" : \"Markdown format - Ready for documentation\"),\n                    outputFormat === \"html\" && (isArabic ? \"تنسيق HTML - جاهز للمواقع الإلكترونية\" : \"HTML format - Ready for websites\"),\n                    outputFormat === \"json\" && (isArabic ? \"تنسيق JSON - جاهز للبرمجة والـ APIs\" : \"JSON format - Ready for programming and APIs\"),\n                    outputFormat === \"yaml\" && (isArabic ? \"تنسيق YAML - جاهز للتكوين والنشر\" : \"YAML format - Ready for configuration and deployment\")\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n        lineNumber: 140,\n        columnNumber: 5\n    }, this);\n}\n_s(OutputPanel, \"sKb4eUZ0iLx4SneSpJIkONUkK08=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore\n    ];\n});\n_c = OutputPanel;\nvar _c;\n$RefreshReg$(_c, \"OutputPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OutputPanel.tsx\n"));

/***/ })

});