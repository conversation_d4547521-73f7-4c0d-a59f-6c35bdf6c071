"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/context-map/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/api/link.js":
/*!********************************************!*\
  !*** ./node_modules/next/dist/api/link.js ***!
  \********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* reexport default from dynamic */ _client_link__WEBPACK_IMPORTED_MODULE_0___default.a; }\n/* harmony export */ });\n/* harmony import */ var _client_link__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/link */ \"(app-pages-browser)/./node_modules/next/dist/client/link.js\");\n/* harmony import */ var _client_link__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_link__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _client_link__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = function(key) { return _client_link__WEBPACK_IMPORTED_MODULE_0__[key]; }.bind(0, __WEBPACK_IMPORT_KEY__)\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n\n//# sourceMappingURL=link.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL2xpbmsuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQXlDO0FBQ1Y7O0FBRS9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL2xpbmsuanM/NWFhYSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBkZWZhdWx0IH0gZnJvbSBcIi4uL2NsaWVudC9saW5rXCI7XG5leHBvcnQgKiBmcm9tIFwiLi4vY2xpZW50L2xpbmtcIjtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bGluay5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/link.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/add-locale.js":
/*!*****************************************************!*\
  !*** ./node_modules/next/dist/client/add-locale.js ***!
  \*****************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"addLocale\", ({\n    enumerable: true,\n    get: function() {\n        return addLocale;\n    }\n}));\nconst _normalizetrailingslash = __webpack_require__(/*! ./normalize-trailing-slash */ \"(app-pages-browser)/./node_modules/next/dist/client/normalize-trailing-slash.js\");\nconst addLocale = function(path) {\n    for(var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n        args[_key - 1] = arguments[_key];\n    }\n    if (false) {}\n    return path;\n};\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=add-locale.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2FkZC1sb2NhbGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs2Q0FHYUE7OztlQUFBQTs7O29EQUY4QjtBQUVwQyxNQUFNQSxZQUF1QixTQUFDQyxJQUFBQTtxQ0FBU0MsT0FBQUEsSUFBQUEsTUFBQUEsT0FBQUEsSUFBQUEsT0FBQUEsSUFBQUEsSUFBQUEsT0FBQUEsR0FBQUEsT0FBQUEsTUFBQUEsT0FBQUE7UUFBQUEsSUFBQUEsQ0FBQUEsT0FBQUEsRUFBQUEsR0FBQUEsU0FBQUEsQ0FBQUEsS0FBQUE7O0lBQzVDLElBQUlDLEtBQStCLEVBQUUsRUFJckM7SUFDQSxPQUFPRjtBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvY2xpZW50L2FkZC1sb2NhbGUudHM/ZmFhZSJdLCJuYW1lcyI6WyJhZGRMb2NhbGUiLCJwYXRoIiwiYXJncyIsInByb2Nlc3MiLCJlbnYiLCJfX05FWFRfSTE4Tl9TVVBQT1JUIiwibm9ybWFsaXplUGF0aFRyYWlsaW5nU2xhc2giLCJyZXF1aXJlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/add-locale.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/get-domain-locale.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/client/get-domain-locale.js ***!
  \************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getDomainLocale\", ({\n    enumerable: true,\n    get: function() {\n        return getDomainLocale;\n    }\n}));\nconst _normalizetrailingslash = __webpack_require__(/*! ./normalize-trailing-slash */ \"(app-pages-browser)/./node_modules/next/dist/client/normalize-trailing-slash.js\");\nconst basePath =  false || \"\";\nfunction getDomainLocale(path, locale, locales, domainLocales) {\n    if (false) {} else {\n        return false;\n    }\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=get-domain-locale.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2dldC1kb21haW4tbG9jYWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7bURBT2dCQTs7O2VBQUFBOzs7b0RBSjJCO0FBRTNDLE1BQU1DLFdBQVdDLE1BQW1DLElBQWU7QUFFNUQsU0FBU0YsZ0JBQ2RLLElBQVksRUFDWkMsTUFBdUIsRUFDdkJDLE9BQWtCLEVBQ2xCQyxhQUE4QjtJQUU5QixJQUFJTixLQUErQixFQUFFLEVBZ0JyQyxNQUFPO1FBQ0wsT0FBTztJQUNUO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3NyYy9jbGllbnQvZ2V0LWRvbWFpbi1sb2NhbGUudHM/MWQ0ZSJdLCJuYW1lcyI6WyJnZXREb21haW5Mb2NhbGUiLCJiYXNlUGF0aCIsInByb2Nlc3MiLCJlbnYiLCJfX05FWFRfUk9VVEVSX0JBU0VQQVRIIiwicGF0aCIsImxvY2FsZSIsImxvY2FsZXMiLCJkb21haW5Mb2NhbGVzIiwiX19ORVhUX0kxOE5fU1VQUE9SVCIsIm5vcm1hbGl6ZUxvY2FsZVBhdGgiLCJyZXF1aXJlIiwiZGV0ZWN0RG9tYWluTG9jYWxlIiwidGFyZ2V0IiwiZGV0ZWN0ZWRMb2NhbGUiLCJkb21haW4iLCJ1bmRlZmluZWQiLCJwcm90byIsImh0dHAiLCJmaW5hbExvY2FsZSIsImRlZmF1bHRMb2NhbGUiLCJub3JtYWxpemVQYXRoVHJhaWxpbmdTbGFzaCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/get-domain-locale.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/link.js":
/*!***********************************************!*\
  !*** ./node_modules/next/dist/client/link.js ***!
  \***********************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _default;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _resolvehref = __webpack_require__(/*! ./resolve-href */ \"(app-pages-browser)/./node_modules/next/dist/client/resolve-href.js\");\nconst _islocalurl = __webpack_require__(/*! ../shared/lib/router/utils/is-local-url */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-local-url.js\");\nconst _formaturl = __webpack_require__(/*! ../shared/lib/router/utils/format-url */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js\");\nconst _utils = __webpack_require__(/*! ../shared/lib/utils */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js\");\nconst _addlocale = __webpack_require__(/*! ./add-locale */ \"(app-pages-browser)/./node_modules/next/dist/client/add-locale.js\");\nconst _routercontextsharedruntime = __webpack_require__(/*! ../shared/lib/router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router-context.shared-runtime.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _useintersection = __webpack_require__(/*! ./use-intersection */ \"(app-pages-browser)/./node_modules/next/dist/client/use-intersection.js\");\nconst _getdomainlocale = __webpack_require__(/*! ./get-domain-locale */ \"(app-pages-browser)/./node_modules/next/dist/client/get-domain-locale.js\");\nconst _addbasepath = __webpack_require__(/*! ./add-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/add-base-path.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./components/router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst prefetched = new Set();\nfunction prefetch(router, href, as, options, appOptions, isAppRouter) {\n    if (typeof window === \"undefined\") {\n        return;\n    }\n    // app-router supports external urls out of the box so it shouldn't short-circuit here as support for e.g. `replace` is added in the app-router.\n    if (!isAppRouter && !(0, _islocalurl.isLocalURL)(href)) {\n        return;\n    }\n    // We should only dedupe requests when experimental.optimisticClientCache is\n    // disabled.\n    if (!options.bypassPrefetchedCheck) {\n        const locale = typeof options.locale !== \"undefined\" ? options.locale : \"locale\" in router ? router.locale : undefined;\n        const prefetchedKey = href + \"%\" + as + \"%\" + locale;\n        // If we've already fetched the key, then don't prefetch it again!\n        if (prefetched.has(prefetchedKey)) {\n            return;\n        }\n        // Mark this URL as prefetched.\n        prefetched.add(prefetchedKey);\n    }\n    const doPrefetch = async ()=>{\n        if (isAppRouter) {\n            // note that `appRouter.prefetch()` is currently sync,\n            // so we have to wrap this call in an async function to be able to catch() errors below.\n            return router.prefetch(href, appOptions);\n        } else {\n            return router.prefetch(href, as, options);\n        }\n    };\n    // Prefetch the JSON page if asked (only in the client)\n    // We need to handle a prefetch error here since we may be\n    // loading with priority which can reject but we don't\n    // want to force navigation since this is only a prefetch\n    doPrefetch().catch((err)=>{\n        if (true) {\n            // rethrow to show invalid URL errors\n            throw err;\n        }\n    });\n}\nfunction isModifiedEvent(event) {\n    const eventTarget = event.currentTarget;\n    const target = eventTarget.getAttribute(\"target\");\n    return target && target !== \"_self\" || event.metaKey || event.ctrlKey || event.shiftKey || event.altKey || // triggers resource download\n    event.nativeEvent && event.nativeEvent.which === 2;\n}\nfunction linkClicked(e, router, href, as, replace, shallow, scroll, locale, isAppRouter) {\n    const { nodeName } = e.currentTarget;\n    // anchors inside an svg have a lowercase nodeName\n    const isAnchorNodeName = nodeName.toUpperCase() === \"A\";\n    if (isAnchorNodeName && (isModifiedEvent(e) || // app-router supports external urls out of the box so it shouldn't short-circuit here as support for e.g. `replace` is added in the app-router.\n    !isAppRouter && !(0, _islocalurl.isLocalURL)(href))) {\n        // ignore click for browser’s default behavior\n        return;\n    }\n    e.preventDefault();\n    const navigate = ()=>{\n        // If the router is an NextRouter instance it will have `beforePopState`\n        const routerScroll = scroll != null ? scroll : true;\n        if (\"beforePopState\" in router) {\n            router[replace ? \"replace\" : \"push\"](href, as, {\n                shallow,\n                locale,\n                scroll: routerScroll\n            });\n        } else {\n            router[replace ? \"replace\" : \"push\"](as || href, {\n                scroll: routerScroll\n            });\n        }\n    };\n    if (isAppRouter) {\n        _react.default.startTransition(navigate);\n    } else {\n        navigate();\n    }\n}\nfunction formatStringOrUrl(urlObjOrString) {\n    if (typeof urlObjOrString === \"string\") {\n        return urlObjOrString;\n    }\n    return (0, _formaturl.formatUrl)(urlObjOrString);\n}\n/**\n * A React component that extends the HTML `<a>` element to provide [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation between routes.\n *\n * It is the primary way to navigate between routes in Next.js.\n *\n * Read more: [Next.js docs: `<Link>`](https://nextjs.org/docs/app/api-reference/components/link)\n */ const Link = /*#__PURE__*/ _s(_react.default.forwardRef(_c = _s(function LinkComponent(props, forwardedRef) {\n    _s();\n    let children;\n    const { href: hrefProp, as: asProp, children: childrenProp, prefetch: prefetchProp = null, passHref, replace, shallow, scroll, locale, onClick, onMouseEnter: onMouseEnterProp, onTouchStart: onTouchStartProp, legacyBehavior = false, ...restProps } = props;\n    children = childrenProp;\n    if (legacyBehavior && (typeof children === \"string\" || typeof children === \"number\")) {\n        children = /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n            children: children\n        });\n    }\n    const pagesRouter = _react.default.useContext(_routercontextsharedruntime.RouterContext);\n    const appRouter = _react.default.useContext(_approutercontextsharedruntime.AppRouterContext);\n    const router = pagesRouter != null ? pagesRouter : appRouter;\n    // We're in the app directory if there is no pages router.\n    const isAppRouter = !pagesRouter;\n    const prefetchEnabled = prefetchProp !== false;\n    /**\n     * The possible states for prefetch are:\n     * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n     * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n     * - false: we will not prefetch if in the viewport at all\n     */ const appPrefetchKind = prefetchProp === null ? _routerreducertypes.PrefetchKind.AUTO : _routerreducertypes.PrefetchKind.FULL;\n    if (true) {\n        function createPropError(args) {\n            return new Error(\"Failed prop type: The prop `\" + args.key + \"` expects a \" + args.expected + \" in `<Link>`, but got `\" + args.actual + \"` instead.\" + (typeof window !== \"undefined\" ? \"\\nOpen your browser's console to view the Component stack trace.\" : \"\"));\n        }\n        // TypeScript trick for type-guarding:\n        const requiredPropsGuard = {\n            href: true\n        };\n        const requiredProps = Object.keys(requiredPropsGuard);\n        requiredProps.forEach((key)=>{\n            if (key === \"href\") {\n                if (props[key] == null || typeof props[key] !== \"string\" && typeof props[key] !== \"object\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`string` or `object`\",\n                        actual: props[key] === null ? \"null\" : typeof props[key]\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n        // TypeScript trick for type-guarding:\n        const optionalPropsGuard = {\n            as: true,\n            replace: true,\n            scroll: true,\n            shallow: true,\n            passHref: true,\n            prefetch: true,\n            locale: true,\n            onClick: true,\n            onMouseEnter: true,\n            onTouchStart: true,\n            legacyBehavior: true\n        };\n        const optionalProps = Object.keys(optionalPropsGuard);\n        optionalProps.forEach((key)=>{\n            const valType = typeof props[key];\n            if (key === \"as\") {\n                if (props[key] && valType !== \"string\" && valType !== \"object\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`string` or `object`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"locale\") {\n                if (props[key] && valType !== \"string\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`string`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"onClick\" || key === \"onMouseEnter\" || key === \"onTouchStart\") {\n                if (props[key] && valType !== \"function\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`function`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"replace\" || key === \"scroll\" || key === \"shallow\" || key === \"passHref\" || key === \"prefetch\" || key === \"legacyBehavior\") {\n                if (props[key] != null && valType !== \"boolean\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`boolean`\",\n                        actual: valType\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n        // This hook is in a conditional but that is ok because `process.env.NODE_ENV` never changes\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const hasWarned = _react.default.useRef(false);\n        if (props.prefetch && !hasWarned.current && !isAppRouter) {\n            hasWarned.current = true;\n            console.warn(\"Next.js auto-prefetches automatically based on viewport. The prefetch attribute is no longer needed. More: https://nextjs.org/docs/messages/prefetch-true-deprecated\");\n        }\n    }\n    if (true) {\n        if (isAppRouter && !asProp) {\n            let href;\n            if (typeof hrefProp === \"string\") {\n                href = hrefProp;\n            } else if (typeof hrefProp === \"object\" && typeof hrefProp.pathname === \"string\") {\n                href = hrefProp.pathname;\n            }\n            if (href) {\n                const hasDynamicSegment = href.split(\"/\").some((segment)=>segment.startsWith(\"[\") && segment.endsWith(\"]\"));\n                if (hasDynamicSegment) {\n                    throw new Error(\"Dynamic href `\" + href + \"` found in <Link> while using the `/app` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href\");\n                }\n            }\n        }\n    }\n    const { href, as } = _react.default.useMemo(()=>{\n        if (!pagesRouter) {\n            const resolvedHref = formatStringOrUrl(hrefProp);\n            return {\n                href: resolvedHref,\n                as: asProp ? formatStringOrUrl(asProp) : resolvedHref\n            };\n        }\n        const [resolvedHref, resolvedAs] = (0, _resolvehref.resolveHref)(pagesRouter, hrefProp, true);\n        return {\n            href: resolvedHref,\n            as: asProp ? (0, _resolvehref.resolveHref)(pagesRouter, asProp) : resolvedAs || resolvedHref\n        };\n    }, [\n        pagesRouter,\n        hrefProp,\n        asProp\n    ]);\n    const previousHref = _react.default.useRef(href);\n    const previousAs = _react.default.useRef(as);\n    // This will return the first child, if multiple are provided it will throw an error\n    let child;\n    if (legacyBehavior) {\n        if (true) {\n            if (onClick) {\n                console.warn('\"onClick\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link');\n            }\n            if (onMouseEnterProp) {\n                console.warn('\"onMouseEnter\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link');\n            }\n            try {\n                child = _react.default.Children.only(children);\n            } catch (err) {\n                if (!children) {\n                    throw new Error(\"No children were passed to <Link> with `href` of `\" + hrefProp + \"` but one child is required https://nextjs.org/docs/messages/link-no-children\");\n                }\n                throw new Error(\"Multiple children were passed to <Link> with `href` of `\" + hrefProp + \"` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children\" + (typeof window !== \"undefined\" ? \" \\nOpen your browser's console to view the Component stack trace.\" : \"\"));\n            }\n        } else {}\n    } else {\n        if (true) {\n            if ((children == null ? void 0 : children.type) === \"a\") {\n                throw new Error(\"Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor\");\n            }\n        }\n    }\n    const childRef = legacyBehavior ? child && typeof child === \"object\" && child.ref : forwardedRef;\n    const [setIntersectionRef, isVisible, resetVisible] = (0, _useintersection.useIntersection)({\n        rootMargin: \"200px\"\n    });\n    const setRef = _react.default.useCallback((el)=>{\n        // Before the link getting observed, check if visible state need to be reset\n        if (previousAs.current !== as || previousHref.current !== href) {\n            resetVisible();\n            previousAs.current = as;\n            previousHref.current = href;\n        }\n        setIntersectionRef(el);\n        if (childRef) {\n            if (typeof childRef === \"function\") childRef(el);\n            else if (typeof childRef === \"object\") {\n                childRef.current = el;\n            }\n        }\n    }, [\n        as,\n        childRef,\n        href,\n        resetVisible,\n        setIntersectionRef\n    ]);\n    // Prefetch the URL if we haven't already and it's visible.\n    _react.default.useEffect(()=>{\n        // in dev, we only prefetch on hover to avoid wasting resources as the prefetch will trigger compiling the page.\n        if (true) {\n            return;\n        }\n        if (!router) {\n            return;\n        }\n        // If we don't need to prefetch the URL, don't do prefetch.\n        if (!isVisible || !prefetchEnabled) {\n            return;\n        }\n        // Prefetch the URL.\n        prefetch(router, href, as, {\n            locale\n        }, {\n            kind: appPrefetchKind\n        }, isAppRouter);\n    }, [\n        as,\n        href,\n        isVisible,\n        locale,\n        prefetchEnabled,\n        pagesRouter == null ? void 0 : pagesRouter.locale,\n        router,\n        isAppRouter,\n        appPrefetchKind\n    ]);\n    const childProps = {\n        ref: setRef,\n        onClick (e) {\n            if (true) {\n                if (!e) {\n                    throw new Error('Component rendered inside next/link has to pass click event to \"onClick\" prop.');\n                }\n            }\n            if (!legacyBehavior && typeof onClick === \"function\") {\n                onClick(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onClick === \"function\") {\n                child.props.onClick(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (e.defaultPrevented) {\n                return;\n            }\n            linkClicked(e, router, href, as, replace, shallow, scroll, locale, isAppRouter);\n        },\n        onMouseEnter (e) {\n            if (!legacyBehavior && typeof onMouseEnterProp === \"function\") {\n                onMouseEnterProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onMouseEnter === \"function\") {\n                child.props.onMouseEnter(e);\n            }\n            if (!router) {\n                return;\n            }\n            if ((!prefetchEnabled || \"development\" === \"development\") && isAppRouter) {\n                return;\n            }\n            prefetch(router, href, as, {\n                locale,\n                priority: true,\n                // @see {https://github.com/vercel/next.js/discussions/40268?sort=top#discussioncomment-3572642}\n                bypassPrefetchedCheck: true\n            }, {\n                kind: appPrefetchKind\n            }, isAppRouter);\n        },\n        onTouchStart:  false ? 0 : function onTouchStart(e) {\n            if (!legacyBehavior && typeof onTouchStartProp === \"function\") {\n                onTouchStartProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onTouchStart === \"function\") {\n                child.props.onTouchStart(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (!prefetchEnabled && isAppRouter) {\n                return;\n            }\n            prefetch(router, href, as, {\n                locale,\n                priority: true,\n                // @see {https://github.com/vercel/next.js/discussions/40268?sort=top#discussioncomment-3572642}\n                bypassPrefetchedCheck: true\n            }, {\n                kind: appPrefetchKind\n            }, isAppRouter);\n        }\n    };\n    // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n    // defined, we specify the current 'href', so that repetition is not needed by the user.\n    // If the url is absolute, we can bypass the logic to prepend the domain and locale.\n    if ((0, _utils.isAbsoluteUrl)(as)) {\n        childProps.href = as;\n    } else if (!legacyBehavior || passHref || child.type === \"a\" && !(\"href\" in child.props)) {\n        const curLocale = typeof locale !== \"undefined\" ? locale : pagesRouter == null ? void 0 : pagesRouter.locale;\n        // we only render domain locales if we are currently on a domain locale\n        // so that locale links are still visitable in development/preview envs\n        const localeDomain = (pagesRouter == null ? void 0 : pagesRouter.isLocaleDomain) && (0, _getdomainlocale.getDomainLocale)(as, curLocale, pagesRouter == null ? void 0 : pagesRouter.locales, pagesRouter == null ? void 0 : pagesRouter.domainLocales);\n        childProps.href = localeDomain || (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(as, curLocale, pagesRouter == null ? void 0 : pagesRouter.defaultLocale));\n    }\n    return legacyBehavior ? /*#__PURE__*/ _react.default.cloneElement(child, childProps) : /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n        ...restProps,\n        ...childProps,\n        children: children\n    });\n}, \"wKD5mb5mk47bkaStGb/Fvd6RWZE=\")), \"wKD5mb5mk47bkaStGb/Fvd6RWZE=\");\n_c1 = Link;\nconst _default = Link;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=link.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"Link$_react.default.forwardRef\");\n$RefreshReg$(_c1, \"Link\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/link.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/request-idle-callback.js":
/*!****************************************************************!*\
  !*** ./node_modules/next/dist/client/request-idle-callback.js ***!
  \****************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    cancelIdleCallback: function() {\n        return cancelIdleCallback;\n    },\n    requestIdleCallback: function() {\n        return requestIdleCallback;\n    }\n});\nconst requestIdleCallback = typeof self !== \"undefined\" && self.requestIdleCallback && self.requestIdleCallback.bind(window) || function(cb) {\n    let start = Date.now();\n    return self.setTimeout(function() {\n        cb({\n            didTimeout: false,\n            timeRemaining: function() {\n                return Math.max(0, 50 - (Date.now() - start));\n            }\n        });\n    }, 1);\n};\nconst cancelIdleCallback = typeof self !== \"undefined\" && self.cancelIdleCallback && self.cancelIdleCallback.bind(window) || function(id) {\n    return clearTimeout(id);\n};\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=request-idle-callback.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L3JlcXVlc3QtaWRsZS1jYWxsYmFjay5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7SUFnQmFBLG9CQUFrQjtlQUFsQkE7O0lBaEJBQyxxQkFBbUI7ZUFBbkJBOzs7QUFBTixNQUFNQSxzQkFDWCxPQUFRQyxTQUFTLGVBQ2ZBLEtBQUtELG1CQUFtQixJQUN4QkMsS0FBS0QsbUJBQW1CLENBQUNFLElBQUksQ0FBQ0MsV0FDaEMsU0FBVUMsRUFBdUI7SUFDL0IsSUFBSUMsUUFBUUMsS0FBS0MsR0FBRztJQUNwQixPQUFPTixLQUFLTyxVQUFVLENBQUM7UUFDckJKLEdBQUc7WUFDREssWUFBWTtZQUNaQyxlQUFlO2dCQUNiLE9BQU9DLEtBQUtDLEdBQUcsQ0FBQyxHQUFHLEtBQU1OLENBQUFBLEtBQUtDLEdBQUcsS0FBS0YsS0FBQUE7WUFDeEM7UUFDRjtJQUNGLEdBQUc7QUFDTDtBQUVLLE1BQU1OLHFCQUNYLE9BQVFFLFNBQVMsZUFDZkEsS0FBS0Ysa0JBQWtCLElBQ3ZCRSxLQUFLRixrQkFBa0IsQ0FBQ0csSUFBSSxDQUFDQyxXQUMvQixTQUFVVSxFQUFVO0lBQ2xCLE9BQU9DLGFBQWFEO0FBQ3RCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvY2xpZW50L3JlcXVlc3QtaWRsZS1jYWxsYmFjay50cz8wNWY0Il0sIm5hbWVzIjpbImNhbmNlbElkbGVDYWxsYmFjayIsInJlcXVlc3RJZGxlQ2FsbGJhY2siLCJzZWxmIiwiYmluZCIsIndpbmRvdyIsImNiIiwic3RhcnQiLCJEYXRlIiwibm93Iiwic2V0VGltZW91dCIsImRpZFRpbWVvdXQiLCJ0aW1lUmVtYWluaW5nIiwiTWF0aCIsIm1heCIsImlkIiwiY2xlYXJUaW1lb3V0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/request-idle-callback.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/resolve-href.js":
/*!*******************************************************!*\
  !*** ./node_modules/next/dist/client/resolve-href.js ***!
  \*******************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"resolveHref\", ({\n    enumerable: true,\n    get: function() {\n        return resolveHref;\n    }\n}));\nconst _querystring = __webpack_require__(/*! ../shared/lib/router/utils/querystring */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/querystring.js\");\nconst _formaturl = __webpack_require__(/*! ../shared/lib/router/utils/format-url */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js\");\nconst _omit = __webpack_require__(/*! ../shared/lib/router/utils/omit */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/omit.js\");\nconst _utils = __webpack_require__(/*! ../shared/lib/utils */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js\");\nconst _normalizetrailingslash = __webpack_require__(/*! ./normalize-trailing-slash */ \"(app-pages-browser)/./node_modules/next/dist/client/normalize-trailing-slash.js\");\nconst _islocalurl = __webpack_require__(/*! ../shared/lib/router/utils/is-local-url */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-local-url.js\");\nconst _utils1 = __webpack_require__(/*! ../shared/lib/router/utils */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/index.js\");\nconst _interpolateas = __webpack_require__(/*! ../shared/lib/router/utils/interpolate-as */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/interpolate-as.js\");\nfunction resolveHref(router, href, resolveAs) {\n    // we use a dummy base url for relative urls\n    let base;\n    let urlAsString = typeof href === \"string\" ? href : (0, _formaturl.formatWithValidation)(href);\n    // repeated slashes and backslashes in the URL are considered\n    // invalid and will never match a Next.js page/file\n    const urlProtoMatch = urlAsString.match(/^[a-zA-Z]{1,}:\\/\\//);\n    const urlAsStringNoProto = urlProtoMatch ? urlAsString.slice(urlProtoMatch[0].length) : urlAsString;\n    const urlParts = urlAsStringNoProto.split(\"?\", 1);\n    if ((urlParts[0] || \"\").match(/(\\/\\/|\\\\)/)) {\n        console.error(\"Invalid href '\" + urlAsString + \"' passed to next/router in page: '\" + router.pathname + \"'. Repeated forward-slashes (//) or backslashes \\\\ are not valid in the href.\");\n        const normalizedUrl = (0, _utils.normalizeRepeatedSlashes)(urlAsStringNoProto);\n        urlAsString = (urlProtoMatch ? urlProtoMatch[0] : \"\") + normalizedUrl;\n    }\n    // Return because it cannot be routed by the Next.js router\n    if (!(0, _islocalurl.isLocalURL)(urlAsString)) {\n        return resolveAs ? [\n            urlAsString\n        ] : urlAsString;\n    }\n    try {\n        base = new URL(urlAsString.startsWith(\"#\") ? router.asPath : router.pathname, \"http://n\");\n    } catch (_) {\n        // fallback to / for invalid asPath values e.g. //\n        base = new URL(\"/\", \"http://n\");\n    }\n    try {\n        const finalUrl = new URL(urlAsString, base);\n        finalUrl.pathname = (0, _normalizetrailingslash.normalizePathTrailingSlash)(finalUrl.pathname);\n        let interpolatedAs = \"\";\n        if ((0, _utils1.isDynamicRoute)(finalUrl.pathname) && finalUrl.searchParams && resolveAs) {\n            const query = (0, _querystring.searchParamsToUrlQuery)(finalUrl.searchParams);\n            const { result, params } = (0, _interpolateas.interpolateAs)(finalUrl.pathname, finalUrl.pathname, query);\n            if (result) {\n                interpolatedAs = (0, _formaturl.formatWithValidation)({\n                    pathname: result,\n                    hash: finalUrl.hash,\n                    query: (0, _omit.omit)(query, params)\n                });\n            }\n        }\n        // if the origin didn't change, it means we received a relative href\n        const resolvedHref = finalUrl.origin === base.origin ? finalUrl.href.slice(finalUrl.origin.length) : finalUrl.href;\n        return resolveAs ? [\n            resolvedHref,\n            interpolatedAs || resolvedHref\n        ] : resolvedHref;\n    } catch (_) {\n        return resolveAs ? [\n            urlAsString\n        ] : urlAsString;\n    }\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=resolve-href.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/resolve-href.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/use-intersection.js":
/*!***********************************************************!*\
  !*** ./node_modules/next/dist/client/use-intersection.js ***!
  \***********************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"useIntersection\", ({\n    enumerable: true,\n    get: function() {\n        return useIntersection;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _requestidlecallback = __webpack_require__(/*! ./request-idle-callback */ \"(app-pages-browser)/./node_modules/next/dist/client/request-idle-callback.js\");\nconst hasIntersectionObserver = typeof IntersectionObserver === \"function\";\nconst observers = new Map();\nconst idList = [];\nfunction createObserver(options) {\n    const id = {\n        root: options.root || null,\n        margin: options.rootMargin || \"\"\n    };\n    const existing = idList.find((obj)=>obj.root === id.root && obj.margin === id.margin);\n    let instance;\n    if (existing) {\n        instance = observers.get(existing);\n        if (instance) {\n            return instance;\n        }\n    }\n    const elements = new Map();\n    const observer = new IntersectionObserver((entries)=>{\n        entries.forEach((entry)=>{\n            const callback = elements.get(entry.target);\n            const isVisible = entry.isIntersecting || entry.intersectionRatio > 0;\n            if (callback && isVisible) {\n                callback(isVisible);\n            }\n        });\n    }, options);\n    instance = {\n        id,\n        observer,\n        elements\n    };\n    idList.push(id);\n    observers.set(id, instance);\n    return instance;\n}\nfunction observe(element, callback, options) {\n    const { id, observer, elements } = createObserver(options);\n    elements.set(element, callback);\n    observer.observe(element);\n    return function unobserve() {\n        elements.delete(element);\n        observer.unobserve(element);\n        // Destroy observer when there's nothing left to watch:\n        if (elements.size === 0) {\n            observer.disconnect();\n            observers.delete(id);\n            const index = idList.findIndex((obj)=>obj.root === id.root && obj.margin === id.margin);\n            if (index > -1) {\n                idList.splice(index, 1);\n            }\n        }\n    };\n}\nfunction useIntersection(param) {\n    let { rootRef, rootMargin, disabled } = param;\n    const isDisabled = disabled || !hasIntersectionObserver;\n    const [visible, setVisible] = (0, _react.useState)(false);\n    const elementRef = (0, _react.useRef)(null);\n    const setElement = (0, _react.useCallback)((element)=>{\n        elementRef.current = element;\n    }, []);\n    (0, _react.useEffect)(()=>{\n        if (hasIntersectionObserver) {\n            if (isDisabled || visible) return;\n            const element = elementRef.current;\n            if (element && element.tagName) {\n                const unobserve = observe(element, (isVisible)=>isVisible && setVisible(isVisible), {\n                    root: rootRef == null ? void 0 : rootRef.current,\n                    rootMargin\n                });\n                return unobserve;\n            }\n        } else {\n            if (!visible) {\n                const idleCallback = (0, _requestidlecallback.requestIdleCallback)(()=>setVisible(true));\n                return ()=>(0, _requestidlecallback.cancelIdleCallback)(idleCallback);\n            }\n        }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        isDisabled,\n        rootMargin,\n        rootRef,\n        visible,\n        elementRef.current\n    ]);\n    const resetVisible = (0, _react.useCallback)(()=>{\n        setVisible(false);\n    }, []);\n    return [\n        setElement,\n        visible,\n        resetVisible\n    ];\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-intersection.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/use-intersection.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/escape-regexp.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/escape-regexp.js ***!
  \************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("// regexp is based on https://github.com/sindresorhus/escape-string-regexp\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"escapeStringRegexp\", ({\n    enumerable: true,\n    get: function() {\n        return escapeStringRegexp;\n    }\n}));\nconst reHasRegExp = /[|\\\\{}()[\\]^$+*?.-]/;\nconst reReplaceRegExp = /[|\\\\{}()[\\]^$+*?.-]/g;\nfunction escapeStringRegexp(str) {\n    // see also: https://github.com/lodash/lodash/blob/2da024c3b4f9947a48517639de7560457cd4ec6c/escapeRegExp.js#L23\n    if (reHasRegExp.test(str)) {\n        return str.replace(reReplaceRegExp, \"\\\\$&\");\n    }\n    return str;\n} //# sourceMappingURL=escape-regexp.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9lc2NhcGUtcmVnZXhwLmpzIiwibWFwcGluZ3MiOiJBQUFBLDBFQUEwRTs7Ozs7c0RBSTFEQTs7O2VBQUFBOzs7QUFIaEIsTUFBTUMsY0FBYztBQUNwQixNQUFNQyxrQkFBa0I7QUFFakIsU0FBU0YsbUJBQW1CRyxHQUFXO0lBQzVDLCtHQUErRztJQUMvRyxJQUFJRixZQUFZRyxJQUFJLENBQUNELE1BQU07UUFDekIsT0FBT0EsSUFBSUUsT0FBTyxDQUFDSCxpQkFBaUI7SUFDdEM7SUFDQSxPQUFPQztBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi8uLi9zcmMvc2hhcmVkL2xpYi9lc2NhcGUtcmVnZXhwLnRzP2RjYjEiXSwibmFtZXMiOlsiZXNjYXBlU3RyaW5nUmVnZXhwIiwicmVIYXNSZWdFeHAiLCJyZVJlcGxhY2VSZWdFeHAiLCJzdHIiLCJ0ZXN0IiwicmVwbGFjZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/escape-regexp.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router-context.shared-runtime.js":
/*!****************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router-context.shared-runtime.js ***!
  \****************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"RouterContext\", ({\n    enumerable: true,\n    get: function() {\n        return RouterContext;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst RouterContext = _react.default.createContext(null);\nif (true) {\n    RouterContext.displayName = \"RouterContext\";\n} //# sourceMappingURL=router-context.shared-runtime.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9yb3V0ZXItY29udGV4dC5zaGFyZWQtcnVudGltZS5qcyIsIm1hcHBpbmdzIjoiOzs7O2lEQUdhQTs7O2VBQUFBOzs7OzRFQUhLO0FBR1gsTUFBTUEsZ0JBQWdCQyxPQUFBQSxPQUFLLENBQUNDLGFBQWEsQ0FBb0I7QUFFcEUsSUFBSUMsSUFBeUIsRUFBYztJQUN6Q0gsY0FBY0ksV0FBVyxHQUFHO0FBQzlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi8uLi9zcmMvc2hhcmVkL2xpYi9yb3V0ZXItY29udGV4dC5zaGFyZWQtcnVudGltZS50cz82MzZjIl0sIm5hbWVzIjpbIlJvdXRlckNvbnRleHQiLCJSZWFjdCIsImNyZWF0ZUNvbnRleHQiLCJwcm9jZXNzIiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router-context.shared-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js":
/*!**********************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/format-url.js ***!
  \**********************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("// Format function modified from nodejs\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    formatUrl: function() {\n        return formatUrl;\n    },\n    formatWithValidation: function() {\n        return formatWithValidation;\n    },\n    urlObjectKeys: function() {\n        return urlObjectKeys;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _querystring = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! ./querystring */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/querystring.js\"));\nconst slashedProtocols = /https?|ftp|gopher|file/;\nfunction formatUrl(urlObj) {\n    let { auth, hostname } = urlObj;\n    let protocol = urlObj.protocol || \"\";\n    let pathname = urlObj.pathname || \"\";\n    let hash = urlObj.hash || \"\";\n    let query = urlObj.query || \"\";\n    let host = false;\n    auth = auth ? encodeURIComponent(auth).replace(/%3A/i, \":\") + \"@\" : \"\";\n    if (urlObj.host) {\n        host = auth + urlObj.host;\n    } else if (hostname) {\n        host = auth + (~hostname.indexOf(\":\") ? \"[\" + hostname + \"]\" : hostname);\n        if (urlObj.port) {\n            host += \":\" + urlObj.port;\n        }\n    }\n    if (query && typeof query === \"object\") {\n        query = String(_querystring.urlQueryToSearchParams(query));\n    }\n    let search = urlObj.search || query && \"?\" + query || \"\";\n    if (protocol && !protocol.endsWith(\":\")) protocol += \":\";\n    if (urlObj.slashes || (!protocol || slashedProtocols.test(protocol)) && host !== false) {\n        host = \"//\" + (host || \"\");\n        if (pathname && pathname[0] !== \"/\") pathname = \"/\" + pathname;\n    } else if (!host) {\n        host = \"\";\n    }\n    if (hash && hash[0] !== \"#\") hash = \"#\" + hash;\n    if (search && search[0] !== \"?\") search = \"?\" + search;\n    pathname = pathname.replace(/[?#]/g, encodeURIComponent);\n    search = search.replace(\"#\", \"%23\");\n    return \"\" + protocol + host + pathname + search + hash;\n}\nconst urlObjectKeys = [\n    \"auth\",\n    \"hash\",\n    \"host\",\n    \"hostname\",\n    \"href\",\n    \"path\",\n    \"pathname\",\n    \"port\",\n    \"protocol\",\n    \"query\",\n    \"search\",\n    \"slashes\"\n];\nfunction formatWithValidation(url) {\n    if (true) {\n        if (url !== null && typeof url === \"object\") {\n            Object.keys(url).forEach((key)=>{\n                if (!urlObjectKeys.includes(key)) {\n                    console.warn(\"Unknown key passed via urlObject into url.format: \" + key);\n                }\n            });\n        }\n    }\n    return formatUrl(url);\n} //# sourceMappingURL=format-url.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/index.js":
/*!*****************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/index.js ***!
  \*****************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    getSortedRoutes: function() {\n        return _sortedroutes.getSortedRoutes;\n    },\n    isDynamicRoute: function() {\n        return _isdynamic.isDynamicRoute;\n    }\n});\nconst _sortedroutes = __webpack_require__(/*! ./sorted-routes */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/sorted-routes.js\");\nconst _isdynamic = __webpack_require__(/*! ./is-dynamic */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-dynamic.js\"); //# sourceMappingURL=index.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9yb3V0ZXIvdXRpbHMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0lBQVNBLGlCQUFlO2VBQWZBLGNBQUFBLGVBQWU7O0lBQ2ZDLGdCQUFjO2VBQWRBLFdBQUFBLGNBQWM7OzswQ0FEUzt1Q0FDRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vLi4vLi4vc3JjL3NoYXJlZC9saWIvcm91dGVyL3V0aWxzL2luZGV4LnRzPzkxMmIiXSwibmFtZXMiOlsiZ2V0U29ydGVkUm91dGVzIiwiaXNEeW5hbWljUm91dGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/interpolate-as.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/interpolate-as.js ***!
  \**************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"interpolateAs\", ({\n    enumerable: true,\n    get: function() {\n        return interpolateAs;\n    }\n}));\nconst _routematcher = __webpack_require__(/*! ./route-matcher */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/route-matcher.js\");\nconst _routeregex = __webpack_require__(/*! ./route-regex */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/route-regex.js\");\nfunction interpolateAs(route, asPathname, query) {\n    let interpolatedRoute = \"\";\n    const dynamicRegex = (0, _routeregex.getRouteRegex)(route);\n    const dynamicGroups = dynamicRegex.groups;\n    const dynamicMatches = (asPathname !== route ? (0, _routematcher.getRouteMatcher)(dynamicRegex)(asPathname) : \"\") || // Fall back to reading the values from the href\n    // TODO: should this take priority; also need to change in the router.\n    query;\n    interpolatedRoute = route;\n    const params = Object.keys(dynamicGroups);\n    if (!params.every((param)=>{\n        let value = dynamicMatches[param] || \"\";\n        const { repeat, optional } = dynamicGroups[param];\n        // support single-level catch-all\n        // TODO: more robust handling for user-error (passing `/`)\n        let replaced = \"[\" + (repeat ? \"...\" : \"\") + param + \"]\";\n        if (optional) {\n            replaced = (!value ? \"/\" : \"\") + \"[\" + replaced + \"]\";\n        }\n        if (repeat && !Array.isArray(value)) value = [\n            value\n        ];\n        return (optional || param in dynamicMatches) && // Interpolate group into data URL if present\n        (interpolatedRoute = interpolatedRoute.replace(replaced, repeat ? value.map(// path delimiter escaped since they are being inserted\n        // into the URL and we expect URL encoded segments\n        // when parsing dynamic route params\n        (segment)=>encodeURIComponent(segment)).join(\"/\") : encodeURIComponent(value)) || \"/\");\n    })) {\n        interpolatedRoute = \"\" // did not satisfy all requirements\n        ;\n    // n.b. We ignore this error because we handle warning for this case in\n    // development in the `<Link>` component directly.\n    }\n    return {\n        params,\n        result: interpolatedRoute\n    };\n} //# sourceMappingURL=interpolate-as.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9yb3V0ZXIvdXRpbHMvaW50ZXJwb2xhdGUtYXMuanMiLCJtYXBwaW5ncyI6Ijs7OztpREFLZ0JBOzs7ZUFBQUE7OzswQ0FIZ0I7d0NBQ0Y7QUFFdkIsU0FBU0EsY0FDZEMsS0FBYSxFQUNiQyxVQUFrQixFQUNsQkMsS0FBcUI7SUFFckIsSUFBSUMsb0JBQW9CO0lBRXhCLE1BQU1DLGVBQWVDLENBQUFBLEdBQUFBLFlBQUFBLGFBQWEsRUFBQ0w7SUFDbkMsTUFBTU0sZ0JBQWdCRixhQUFhRyxNQUFNO0lBQ3pDLE1BQU1DLGlCQUVIUCxDQUFBQSxlQUFlRCxRQUFRUyxDQUFBQSxHQUFBQSxjQUFBQSxlQUFlLEVBQUNMLGNBQWNILGNBQWMsT0FDcEUsZ0RBQWdEO0lBQ2hELHNFQUFzRTtJQUN0RUM7SUFFRkMsb0JBQW9CSDtJQUNwQixNQUFNVSxTQUFTQyxPQUFPQyxJQUFJLENBQUNOO0lBRTNCLElBQ0UsQ0FBQ0ksT0FBT0csS0FBSyxDQUFDLENBQUNDO1FBQ2IsSUFBSUMsUUFBUVAsY0FBYyxDQUFDTSxNQUFNLElBQUk7UUFDckMsTUFBTSxFQUFFRSxNQUFNLEVBQUVDLFFBQVEsRUFBRSxHQUFHWCxhQUFhLENBQUNRLE1BQU07UUFFakQsaUNBQWlDO1FBQ2pDLDBEQUEwRDtRQUMxRCxJQUFJSSxXQUFXLE1BQUlGLENBQUFBLFNBQVMsUUFBUSxNQUFLRixRQUFNO1FBQy9DLElBQUlHLFVBQVU7WUFDWkMsV0FBVyxDQUFHLENBQUNILFFBQVEsTUFBTSxNQUFHLE1BQUdHLFdBQVM7UUFDOUM7UUFDQSxJQUFJRixVQUFVLENBQUNHLE1BQU1DLE9BQU8sQ0FBQ0wsUUFBUUEsUUFBUTtZQUFDQTtTQUFNO1FBRXBELE9BQ0UsQ0FBQ0UsWUFBWUgsU0FBU04sY0FBQUEsS0FDdEIsNkNBQTZDO1FBQzVDTCxDQUFBQSxvQkFDQ0Esa0JBQW1Ca0IsT0FBTyxDQUN4QkgsVUFDQUYsU0FDSUQsTUFDR08sR0FBRyxDQUVGLHVEQUF1RDtRQUN2RCxrREFBa0Q7UUFDbEQsb0NBQW9DO1FBQ3BDLENBQUNDLFVBQVlDLG1CQUFtQkQsVUFFakNFLElBQUksQ0FBQyxPQUNSRCxtQkFBbUJULFdBQ3BCO0lBRVgsSUFDQTtRQUNBWixvQkFBb0IsR0FBRyxtQ0FBbUM7O0lBRTFELHVFQUF1RTtJQUN2RSxrREFBa0Q7SUFDcEQ7SUFDQSxPQUFPO1FBQ0xPO1FBQ0FnQixRQUFRdkI7SUFDVjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi8uLi8uLi9zcmMvc2hhcmVkL2xpYi9yb3V0ZXIvdXRpbHMvaW50ZXJwb2xhdGUtYXMudHM/ODUyZCJdLCJuYW1lcyI6WyJpbnRlcnBvbGF0ZUFzIiwicm91dGUiLCJhc1BhdGhuYW1lIiwicXVlcnkiLCJpbnRlcnBvbGF0ZWRSb3V0ZSIsImR5bmFtaWNSZWdleCIsImdldFJvdXRlUmVnZXgiLCJkeW5hbWljR3JvdXBzIiwiZ3JvdXBzIiwiZHluYW1pY01hdGNoZXMiLCJnZXRSb3V0ZU1hdGNoZXIiLCJwYXJhbXMiLCJPYmplY3QiLCJrZXlzIiwiZXZlcnkiLCJwYXJhbSIsInZhbHVlIiwicmVwZWF0Iiwib3B0aW9uYWwiLCJyZXBsYWNlZCIsIkFycmF5IiwiaXNBcnJheSIsInJlcGxhY2UiLCJtYXAiLCJzZWdtZW50IiwiZW5jb2RlVVJJQ29tcG9uZW50Iiwiam9pbiIsInJlc3VsdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/interpolate-as.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-dynamic.js":
/*!**********************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/is-dynamic.js ***!
  \**********************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"isDynamicRoute\", ({\n    enumerable: true,\n    get: function() {\n        return isDynamicRoute;\n    }\n}));\nconst _interceptionroutes = __webpack_require__(/*! ../../../../server/future/helpers/interception-routes */ \"(app-pages-browser)/./node_modules/next/dist/server/future/helpers/interception-routes.js\");\n// Identify /[param]/ in route string\nconst TEST_ROUTE = /\\/\\[[^/]+?\\](?=\\/|$)/;\nfunction isDynamicRoute(route) {\n    if ((0, _interceptionroutes.isInterceptionRouteAppPath)(route)) {\n        route = (0, _interceptionroutes.extractInterceptionRouteInformation)(route).interceptedRoute;\n    }\n    return TEST_ROUTE.test(route);\n} //# sourceMappingURL=is-dynamic.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9yb3V0ZXIvdXRpbHMvaXMtZHluYW1pYy5qcyIsIm1hcHBpbmdzIjoiOzs7O2tEQVFnQkE7OztlQUFBQTs7O2dEQUxUO0FBRVAscUNBQXFDO0FBQ3JDLE1BQU1DLGFBQWE7QUFFWixTQUFTRCxlQUFlRSxLQUFhO0lBQzFDLElBQUlDLENBQUFBLEdBQUFBLG9CQUFBQSwwQkFBMEIsRUFBQ0QsUUFBUTtRQUNyQ0EsUUFBUUUsQ0FBQUEsR0FBQUEsb0JBQUFBLG1DQUFtQyxFQUFDRixPQUFPRyxnQkFBZ0I7SUFDckU7SUFFQSxPQUFPSixXQUFXSyxJQUFJLENBQUNKO0FBQ3pCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi8uLi8uLi9zcmMvc2hhcmVkL2xpYi9yb3V0ZXIvdXRpbHMvaXMtZHluYW1pYy50cz8xNDY1Il0sIm5hbWVzIjpbImlzRHluYW1pY1JvdXRlIiwiVEVTVF9ST1VURSIsInJvdXRlIiwiaXNJbnRlcmNlcHRpb25Sb3V0ZUFwcFBhdGgiLCJleHRyYWN0SW50ZXJjZXB0aW9uUm91dGVJbmZvcm1hdGlvbiIsImludGVyY2VwdGVkUm91dGUiLCJ0ZXN0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-dynamic.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-local-url.js":
/*!************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/is-local-url.js ***!
  \************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"isLocalURL\", ({\n    enumerable: true,\n    get: function() {\n        return isLocalURL;\n    }\n}));\nconst _utils = __webpack_require__(/*! ../../utils */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js\");\nconst _hasbasepath = __webpack_require__(/*! ../../../../client/has-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/has-base-path.js\");\nfunction isLocalURL(url) {\n    // prevent a hydration mismatch on href for url with anchor refs\n    if (!(0, _utils.isAbsoluteUrl)(url)) return true;\n    try {\n        // absolute urls can be local if they are on the same origin\n        const locationOrigin = (0, _utils.getLocationOrigin)();\n        const resolved = new URL(url, locationOrigin);\n        return resolved.origin === locationOrigin && (0, _hasbasepath.hasBasePath)(resolved.pathname);\n    } catch (_) {\n        return false;\n    }\n} //# sourceMappingURL=is-local-url.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9yb3V0ZXIvdXRpbHMvaXMtbG9jYWwtdXJsLmpzIiwibWFwcGluZ3MiOiI7Ozs7OENBTWdCQTs7O2VBQUFBOzs7bUNBTmlDO3lDQUNyQjtBQUtyQixTQUFTQSxXQUFXQyxHQUFXO0lBQ3BDLGdFQUFnRTtJQUNoRSxJQUFJLENBQUNDLENBQUFBLEdBQUFBLE9BQUFBLGFBQWEsRUFBQ0QsTUFBTSxPQUFPO0lBQ2hDLElBQUk7UUFDRiw0REFBNEQ7UUFDNUQsTUFBTUUsaUJBQWlCQyxDQUFBQSxHQUFBQSxPQUFBQSxpQkFBaUI7UUFDeEMsTUFBTUMsV0FBVyxJQUFJQyxJQUFJTCxLQUFLRTtRQUM5QixPQUFPRSxTQUFTRSxNQUFNLEtBQUtKLGtCQUFrQkssQ0FBQUEsR0FBQUEsYUFBQUEsV0FBVyxFQUFDSCxTQUFTSSxRQUFRO0lBQzVFLEVBQUUsT0FBT0MsR0FBRztRQUNWLE9BQU87SUFDVDtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi8uLi8uLi9zcmMvc2hhcmVkL2xpYi9yb3V0ZXIvdXRpbHMvaXMtbG9jYWwtdXJsLnRzP2UxYzIiXSwibmFtZXMiOlsiaXNMb2NhbFVSTCIsInVybCIsImlzQWJzb2x1dGVVcmwiLCJsb2NhdGlvbk9yaWdpbiIsImdldExvY2F0aW9uT3JpZ2luIiwicmVzb2x2ZWQiLCJVUkwiLCJvcmlnaW4iLCJoYXNCYXNlUGF0aCIsInBhdGhuYW1lIiwiXyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-local-url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/omit.js":
/*!****************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/omit.js ***!
  \****************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"omit\", ({\n    enumerable: true,\n    get: function() {\n        return omit;\n    }\n}));\nfunction omit(object, keys) {\n    const omitted = {};\n    Object.keys(object).forEach((key)=>{\n        if (!keys.includes(key)) {\n            omitted[key] = object[key];\n        }\n    });\n    return omitted;\n} //# sourceMappingURL=omit.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9yb3V0ZXIvdXRpbHMvb21pdC5qcyIsIm1hcHBpbmdzIjoiOzs7O3dDQUFnQkE7OztlQUFBQTs7O0FBQVQsU0FBU0EsS0FDZEMsTUFBUyxFQUNUQyxJQUFTO0lBRVQsTUFBTUMsVUFBc0MsQ0FBQztJQUM3Q0MsT0FBT0YsSUFBSSxDQUFDRCxRQUFRSSxPQUFPLENBQUMsQ0FBQ0M7UUFDM0IsSUFBSSxDQUFDSixLQUFLSyxRQUFRLENBQUNELE1BQVc7WUFDNUJILE9BQU8sQ0FBQ0csSUFBSSxHQUFHTCxNQUFNLENBQUNLLElBQUk7UUFDNUI7SUFDRjtJQUNBLE9BQU9IO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uLy4uLy4uL3NyYy9zaGFyZWQvbGliL3JvdXRlci91dGlscy9vbWl0LnRzP2Y3ZWEiXSwibmFtZXMiOlsib21pdCIsIm9iamVjdCIsImtleXMiLCJvbWl0dGVkIiwiT2JqZWN0IiwiZm9yRWFjaCIsImtleSIsImluY2x1ZGVzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/omit.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/querystring.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/querystring.js ***!
  \***********************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    assign: function() {\n        return assign;\n    },\n    searchParamsToUrlQuery: function() {\n        return searchParamsToUrlQuery;\n    },\n    urlQueryToSearchParams: function() {\n        return urlQueryToSearchParams;\n    }\n});\nfunction searchParamsToUrlQuery(searchParams) {\n    const query = {};\n    searchParams.forEach((value, key)=>{\n        if (typeof query[key] === \"undefined\") {\n            query[key] = value;\n        } else if (Array.isArray(query[key])) {\n            query[key].push(value);\n        } else {\n            query[key] = [\n                query[key],\n                value\n            ];\n        }\n    });\n    return query;\n}\nfunction stringifyUrlQueryParam(param) {\n    if (typeof param === \"string\" || typeof param === \"number\" && !isNaN(param) || typeof param === \"boolean\") {\n        return String(param);\n    } else {\n        return \"\";\n    }\n}\nfunction urlQueryToSearchParams(urlQuery) {\n    const result = new URLSearchParams();\n    Object.entries(urlQuery).forEach((param)=>{\n        let [key, value] = param;\n        if (Array.isArray(value)) {\n            value.forEach((item)=>result.append(key, stringifyUrlQueryParam(item)));\n        } else {\n            result.set(key, stringifyUrlQueryParam(value));\n        }\n    });\n    return result;\n}\nfunction assign(target) {\n    for(var _len = arguments.length, searchParamsList = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n        searchParamsList[_key - 1] = arguments[_key];\n    }\n    searchParamsList.forEach((searchParams)=>{\n        Array.from(searchParams.keys()).forEach((key)=>target.delete(key));\n        searchParams.forEach((value, key)=>target.append(key, value));\n    });\n    return target;\n} //# sourceMappingURL=querystring.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/querystring.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/route-matcher.js":
/*!*************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/route-matcher.js ***!
  \*************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getRouteMatcher\", ({\n    enumerable: true,\n    get: function() {\n        return getRouteMatcher;\n    }\n}));\nconst _utils = __webpack_require__(/*! ../../utils */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js\");\nfunction getRouteMatcher(param) {\n    let { re, groups } = param;\n    return (pathname)=>{\n        const routeMatch = re.exec(pathname);\n        if (!routeMatch) {\n            return false;\n        }\n        const decode = (param)=>{\n            try {\n                return decodeURIComponent(param);\n            } catch (_) {\n                throw new _utils.DecodeError(\"failed to decode param\");\n            }\n        };\n        const params = {};\n        Object.keys(groups).forEach((slugName)=>{\n            const g = groups[slugName];\n            const m = routeMatch[g.pos];\n            if (m !== undefined) {\n                params[slugName] = ~m.indexOf(\"/\") ? m.split(\"/\").map((entry)=>decode(entry)) : g.repeat ? [\n                    decode(m)\n                ] : decode(m);\n            }\n        });\n        return params;\n    };\n} //# sourceMappingURL=route-matcher.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9yb3V0ZXIvdXRpbHMvcm91dGUtbWF0Y2hlci5qcyIsIm1hcHBpbmdzIjoiOzs7O21EQVdnQkE7OztlQUFBQTs7O21DQVZZO0FBVXJCLFNBQVNBLGdCQUFnQkMsS0FBMEI7SUFBMUIsTUFBRUMsRUFBRSxFQUFFQyxNQUFNLEVBQWMsR0FBMUJGO0lBQzlCLE9BQU8sQ0FBQ0c7UUFDTixNQUFNQyxhQUFhSCxHQUFHSSxJQUFJLENBQUNGO1FBQzNCLElBQUksQ0FBQ0MsWUFBWTtZQUNmLE9BQU87UUFDVDtRQUVBLE1BQU1FLFNBQVMsQ0FBQ047WUFDZCxJQUFJO2dCQUNGLE9BQU9PLG1CQUFtQlA7WUFDNUIsRUFBRSxPQUFPUSxHQUFHO2dCQUNWLE1BQU0sSUFBSUMsT0FBQUEsV0FBVyxDQUFDO1lBQ3hCO1FBQ0Y7UUFDQSxNQUFNQyxTQUFxRCxDQUFDO1FBRTVEQyxPQUFPQyxJQUFJLENBQUNWLFFBQVFXLE9BQU8sQ0FBQyxDQUFDQztZQUMzQixNQUFNQyxJQUFJYixNQUFNLENBQUNZLFNBQVM7WUFDMUIsTUFBTUUsSUFBSVosVUFBVSxDQUFDVyxFQUFFRSxHQUFHLENBQUM7WUFDM0IsSUFBSUQsTUFBTUUsV0FBVztnQkFDbkJSLE1BQU0sQ0FBQ0ksU0FBUyxHQUFHLENBQUNFLEVBQUVHLE9BQU8sQ0FBQyxPQUMxQkgsRUFBRUksS0FBSyxDQUFDLEtBQUtDLEdBQUcsQ0FBQyxDQUFDQyxRQUFVaEIsT0FBT2dCLFVBQ25DUCxFQUFFUSxNQUFNLEdBQ1I7b0JBQUNqQixPQUFPVTtpQkFBRyxHQUNYVixPQUFPVTtZQUNiO1FBQ0Y7UUFDQSxPQUFPTjtJQUNUO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uLy4uLy4uL3NyYy9zaGFyZWQvbGliL3JvdXRlci91dGlscy9yb3V0ZS1tYXRjaGVyLnRzP2JlZmYiXSwibmFtZXMiOlsiZ2V0Um91dGVNYXRjaGVyIiwicGFyYW0iLCJyZSIsImdyb3VwcyIsInBhdGhuYW1lIiwicm91dGVNYXRjaCIsImV4ZWMiLCJkZWNvZGUiLCJkZWNvZGVVUklDb21wb25lbnQiLCJfIiwiRGVjb2RlRXJyb3IiLCJwYXJhbXMiLCJPYmplY3QiLCJrZXlzIiwiZm9yRWFjaCIsInNsdWdOYW1lIiwiZyIsIm0iLCJwb3MiLCJ1bmRlZmluZWQiLCJpbmRleE9mIiwic3BsaXQiLCJtYXAiLCJlbnRyeSIsInJlcGVhdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/route-matcher.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/route-regex.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/route-regex.js ***!
  \***********************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    getNamedMiddlewareRegex: function() {\n        return getNamedMiddlewareRegex;\n    },\n    getNamedRouteRegex: function() {\n        return getNamedRouteRegex;\n    },\n    getRouteRegex: function() {\n        return getRouteRegex;\n    }\n});\nconst _interceptionroutes = __webpack_require__(/*! ../../../../server/future/helpers/interception-routes */ \"(app-pages-browser)/./node_modules/next/dist/server/future/helpers/interception-routes.js\");\nconst _escaperegexp = __webpack_require__(/*! ../../escape-regexp */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/escape-regexp.js\");\nconst _removetrailingslash = __webpack_require__(/*! ./remove-trailing-slash */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/remove-trailing-slash.js\");\nconst NEXT_QUERY_PARAM_PREFIX = \"nxtP\";\nconst NEXT_INTERCEPTION_MARKER_PREFIX = \"nxtI\";\n/**\n * Parses a given parameter from a route to a data structure that can be used\n * to generate the parametrized route. Examples:\n *   - `[...slug]` -> `{ key: 'slug', repeat: true, optional: true }`\n *   - `...slug` -> `{ key: 'slug', repeat: true, optional: false }`\n *   - `[foo]` -> `{ key: 'foo', repeat: false, optional: true }`\n *   - `bar` -> `{ key: 'bar', repeat: false, optional: false }`\n */ function parseParameter(param) {\n    const optional = param.startsWith(\"[\") && param.endsWith(\"]\");\n    if (optional) {\n        param = param.slice(1, -1);\n    }\n    const repeat = param.startsWith(\"...\");\n    if (repeat) {\n        param = param.slice(3);\n    }\n    return {\n        key: param,\n        repeat,\n        optional\n    };\n}\nfunction getParametrizedRoute(route) {\n    const segments = (0, _removetrailingslash.removeTrailingSlash)(route).slice(1).split(\"/\");\n    const groups = {};\n    let groupIndex = 1;\n    return {\n        parameterizedRoute: segments.map((segment)=>{\n            const markerMatch = _interceptionroutes.INTERCEPTION_ROUTE_MARKERS.find((m)=>segment.startsWith(m));\n            const paramMatches = segment.match(/\\[((?:\\[.*\\])|.+)\\]/) // Check for parameters\n            ;\n            if (markerMatch && paramMatches) {\n                const { key, optional, repeat } = parseParameter(paramMatches[1]);\n                groups[key] = {\n                    pos: groupIndex++,\n                    repeat,\n                    optional\n                };\n                return \"/\" + (0, _escaperegexp.escapeStringRegexp)(markerMatch) + \"([^/]+?)\";\n            } else if (paramMatches) {\n                const { key, repeat, optional } = parseParameter(paramMatches[1]);\n                groups[key] = {\n                    pos: groupIndex++,\n                    repeat,\n                    optional\n                };\n                return repeat ? optional ? \"(?:/(.+?))?\" : \"/(.+?)\" : \"/([^/]+?)\";\n            } else {\n                return \"/\" + (0, _escaperegexp.escapeStringRegexp)(segment);\n            }\n        }).join(\"\"),\n        groups\n    };\n}\nfunction getRouteRegex(normalizedRoute) {\n    const { parameterizedRoute, groups } = getParametrizedRoute(normalizedRoute);\n    return {\n        re: new RegExp(\"^\" + parameterizedRoute + \"(?:/)?$\"),\n        groups: groups\n    };\n}\n/**\n * Builds a function to generate a minimal routeKey using only a-z and minimal\n * number of characters.\n */ function buildGetSafeRouteKey() {\n    let i = 0;\n    return ()=>{\n        let routeKey = \"\";\n        let j = ++i;\n        while(j > 0){\n            routeKey += String.fromCharCode(97 + (j - 1) % 26);\n            j = Math.floor((j - 1) / 26);\n        }\n        return routeKey;\n    };\n}\nfunction getSafeKeyFromSegment(param) {\n    let { interceptionMarker, getSafeRouteKey, segment, routeKeys, keyPrefix } = param;\n    const { key, optional, repeat } = parseParameter(segment);\n    // replace any non-word characters since they can break\n    // the named regex\n    let cleanedKey = key.replace(/\\W/g, \"\");\n    if (keyPrefix) {\n        cleanedKey = \"\" + keyPrefix + cleanedKey;\n    }\n    let invalidKey = false;\n    // check if the key is still invalid and fallback to using a known\n    // safe key\n    if (cleanedKey.length === 0 || cleanedKey.length > 30) {\n        invalidKey = true;\n    }\n    if (!isNaN(parseInt(cleanedKey.slice(0, 1)))) {\n        invalidKey = true;\n    }\n    if (invalidKey) {\n        cleanedKey = getSafeRouteKey();\n    }\n    if (keyPrefix) {\n        routeKeys[cleanedKey] = \"\" + keyPrefix + key;\n    } else {\n        routeKeys[cleanedKey] = key;\n    }\n    // if the segment has an interception marker, make sure that's part of the regex pattern\n    // this is to ensure that the route with the interception marker doesn't incorrectly match\n    // the non-intercepted route (ie /app/(.)[username] should not match /app/[username])\n    const interceptionPrefix = interceptionMarker ? (0, _escaperegexp.escapeStringRegexp)(interceptionMarker) : \"\";\n    return repeat ? optional ? \"(?:/\" + interceptionPrefix + \"(?<\" + cleanedKey + \">.+?))?\" : \"/\" + interceptionPrefix + \"(?<\" + cleanedKey + \">.+?)\" : \"/\" + interceptionPrefix + \"(?<\" + cleanedKey + \">[^/]+?)\";\n}\nfunction getNamedParametrizedRoute(route, prefixRouteKeys) {\n    const segments = (0, _removetrailingslash.removeTrailingSlash)(route).slice(1).split(\"/\");\n    const getSafeRouteKey = buildGetSafeRouteKey();\n    const routeKeys = {};\n    return {\n        namedParameterizedRoute: segments.map((segment)=>{\n            const hasInterceptionMarker = _interceptionroutes.INTERCEPTION_ROUTE_MARKERS.some((m)=>segment.startsWith(m));\n            const paramMatches = segment.match(/\\[((?:\\[.*\\])|.+)\\]/) // Check for parameters\n            ;\n            if (hasInterceptionMarker && paramMatches) {\n                const [usedMarker] = segment.split(paramMatches[0]);\n                return getSafeKeyFromSegment({\n                    getSafeRouteKey,\n                    interceptionMarker: usedMarker,\n                    segment: paramMatches[1],\n                    routeKeys,\n                    keyPrefix: prefixRouteKeys ? NEXT_INTERCEPTION_MARKER_PREFIX : undefined\n                });\n            } else if (paramMatches) {\n                return getSafeKeyFromSegment({\n                    getSafeRouteKey,\n                    segment: paramMatches[1],\n                    routeKeys,\n                    keyPrefix: prefixRouteKeys ? NEXT_QUERY_PARAM_PREFIX : undefined\n                });\n            } else {\n                return \"/\" + (0, _escaperegexp.escapeStringRegexp)(segment);\n            }\n        }).join(\"\"),\n        routeKeys\n    };\n}\nfunction getNamedRouteRegex(normalizedRoute, prefixRouteKey) {\n    const result = getNamedParametrizedRoute(normalizedRoute, prefixRouteKey);\n    return {\n        ...getRouteRegex(normalizedRoute),\n        namedRegex: \"^\" + result.namedParameterizedRoute + \"(?:/)?$\",\n        routeKeys: result.routeKeys\n    };\n}\nfunction getNamedMiddlewareRegex(normalizedRoute, options) {\n    const { parameterizedRoute } = getParametrizedRoute(normalizedRoute);\n    const { catchAll = true } = options;\n    if (parameterizedRoute === \"/\") {\n        let catchAllRegex = catchAll ? \".*\" : \"\";\n        return {\n            namedRegex: \"^/\" + catchAllRegex + \"$\"\n        };\n    }\n    const { namedParameterizedRoute } = getNamedParametrizedRoute(normalizedRoute, false);\n    let catchAllGroupedRegex = catchAll ? \"(?:(/.*)?)\" : \"\";\n    return {\n        namedRegex: \"^\" + namedParameterizedRoute + catchAllGroupedRegex + \"$\"\n    };\n} //# sourceMappingURL=route-regex.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/route-regex.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/sorted-routes.js":
/*!*************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/sorted-routes.js ***!
  \*************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getSortedRoutes\", ({\n    enumerable: true,\n    get: function() {\n        return getSortedRoutes;\n    }\n}));\nclass UrlNode {\n    insert(urlPath) {\n        this._insert(urlPath.split(\"/\").filter(Boolean), [], false);\n    }\n    smoosh() {\n        return this._smoosh();\n    }\n    _smoosh(prefix) {\n        if (prefix === void 0) prefix = \"/\";\n        const childrenPaths = [\n            ...this.children.keys()\n        ].sort();\n        if (this.slugName !== null) {\n            childrenPaths.splice(childrenPaths.indexOf(\"[]\"), 1);\n        }\n        if (this.restSlugName !== null) {\n            childrenPaths.splice(childrenPaths.indexOf(\"[...]\"), 1);\n        }\n        if (this.optionalRestSlugName !== null) {\n            childrenPaths.splice(childrenPaths.indexOf(\"[[...]]\"), 1);\n        }\n        const routes = childrenPaths.map((c)=>this.children.get(c)._smoosh(\"\" + prefix + c + \"/\")).reduce((prev, curr)=>[\n                ...prev,\n                ...curr\n            ], []);\n        if (this.slugName !== null) {\n            routes.push(...this.children.get(\"[]\")._smoosh(prefix + \"[\" + this.slugName + \"]/\"));\n        }\n        if (!this.placeholder) {\n            const r = prefix === \"/\" ? \"/\" : prefix.slice(0, -1);\n            if (this.optionalRestSlugName != null) {\n                throw new Error('You cannot define a route with the same specificity as a optional catch-all route (\"' + r + '\" and \"' + r + \"[[...\" + this.optionalRestSlugName + ']]\").');\n            }\n            routes.unshift(r);\n        }\n        if (this.restSlugName !== null) {\n            routes.push(...this.children.get(\"[...]\")._smoosh(prefix + \"[...\" + this.restSlugName + \"]/\"));\n        }\n        if (this.optionalRestSlugName !== null) {\n            routes.push(...this.children.get(\"[[...]]\")._smoosh(prefix + \"[[...\" + this.optionalRestSlugName + \"]]/\"));\n        }\n        return routes;\n    }\n    _insert(urlPaths, slugNames, isCatchAll) {\n        if (urlPaths.length === 0) {\n            this.placeholder = false;\n            return;\n        }\n        if (isCatchAll) {\n            throw new Error(\"Catch-all must be the last part of the URL.\");\n        }\n        // The next segment in the urlPaths list\n        let nextSegment = urlPaths[0];\n        // Check if the segment matches `[something]`\n        if (nextSegment.startsWith(\"[\") && nextSegment.endsWith(\"]\")) {\n            // Strip `[` and `]`, leaving only `something`\n            let segmentName = nextSegment.slice(1, -1);\n            let isOptional = false;\n            if (segmentName.startsWith(\"[\") && segmentName.endsWith(\"]\")) {\n                // Strip optional `[` and `]`, leaving only `something`\n                segmentName = segmentName.slice(1, -1);\n                isOptional = true;\n            }\n            if (segmentName.startsWith(\"...\")) {\n                // Strip `...`, leaving only `something`\n                segmentName = segmentName.substring(3);\n                isCatchAll = true;\n            }\n            if (segmentName.startsWith(\"[\") || segmentName.endsWith(\"]\")) {\n                throw new Error(\"Segment names may not start or end with extra brackets ('\" + segmentName + \"').\");\n            }\n            if (segmentName.startsWith(\".\")) {\n                throw new Error(\"Segment names may not start with erroneous periods ('\" + segmentName + \"').\");\n            }\n            function handleSlug(previousSlug, nextSlug) {\n                if (previousSlug !== null) {\n                    // If the specific segment already has a slug but the slug is not `something`\n                    // This prevents collisions like:\n                    // pages/[post]/index.js\n                    // pages/[id]/index.js\n                    // Because currently multiple dynamic params on the same segment level are not supported\n                    if (previousSlug !== nextSlug) {\n                        // TODO: This error seems to be confusing for users, needs an error link, the description can be based on above comment.\n                        throw new Error(\"You cannot use different slug names for the same dynamic path ('\" + previousSlug + \"' !== '\" + nextSlug + \"').\");\n                    }\n                }\n                slugNames.forEach((slug)=>{\n                    if (slug === nextSlug) {\n                        throw new Error('You cannot have the same slug name \"' + nextSlug + '\" repeat within a single dynamic path');\n                    }\n                    if (slug.replace(/\\W/g, \"\") === nextSegment.replace(/\\W/g, \"\")) {\n                        throw new Error('You cannot have the slug names \"' + slug + '\" and \"' + nextSlug + '\" differ only by non-word symbols within a single dynamic path');\n                    }\n                });\n                slugNames.push(nextSlug);\n            }\n            if (isCatchAll) {\n                if (isOptional) {\n                    if (this.restSlugName != null) {\n                        throw new Error('You cannot use both an required and optional catch-all route at the same level (\"[...' + this.restSlugName + ']\" and \"' + urlPaths[0] + '\" ).');\n                    }\n                    handleSlug(this.optionalRestSlugName, segmentName);\n                    // slugName is kept as it can only be one particular slugName\n                    this.optionalRestSlugName = segmentName;\n                    // nextSegment is overwritten to [[...]] so that it can later be sorted specifically\n                    nextSegment = \"[[...]]\";\n                } else {\n                    if (this.optionalRestSlugName != null) {\n                        throw new Error('You cannot use both an optional and required catch-all route at the same level (\"[[...' + this.optionalRestSlugName + ']]\" and \"' + urlPaths[0] + '\").');\n                    }\n                    handleSlug(this.restSlugName, segmentName);\n                    // slugName is kept as it can only be one particular slugName\n                    this.restSlugName = segmentName;\n                    // nextSegment is overwritten to [...] so that it can later be sorted specifically\n                    nextSegment = \"[...]\";\n                }\n            } else {\n                if (isOptional) {\n                    throw new Error('Optional route parameters are not yet supported (\"' + urlPaths[0] + '\").');\n                }\n                handleSlug(this.slugName, segmentName);\n                // slugName is kept as it can only be one particular slugName\n                this.slugName = segmentName;\n                // nextSegment is overwritten to [] so that it can later be sorted specifically\n                nextSegment = \"[]\";\n            }\n        }\n        // If this UrlNode doesn't have the nextSegment yet we create a new child UrlNode\n        if (!this.children.has(nextSegment)) {\n            this.children.set(nextSegment, new UrlNode());\n        }\n        this.children.get(nextSegment)._insert(urlPaths.slice(1), slugNames, isCatchAll);\n    }\n    constructor(){\n        this.placeholder = true;\n        this.children = new Map();\n        this.slugName = null;\n        this.restSlugName = null;\n        this.optionalRestSlugName = null;\n    }\n}\nfunction getSortedRoutes(normalizedPages) {\n    // First the UrlNode is created, and every UrlNode can have only 1 dynamic segment\n    // Eg you can't have pages/[post]/abc.js and pages/[hello]/something-else.js\n    // Only 1 dynamic segment per nesting level\n    // So in the case that is test/integration/dynamic-routing it'll be this:\n    // pages/[post]/comments.js\n    // pages/blog/[post]/comment/[id].js\n    // Both are fine because `pages/[post]` and `pages/blog` are on the same level\n    // So in this case `UrlNode` created here has `this.slugName === 'post'`\n    // And since your PR passed through `slugName` as an array basically it'd including it in too many possibilities\n    // Instead what has to be passed through is the upwards path's dynamic names\n    const root = new UrlNode();\n    // Here the `root` gets injected multiple paths, and insert will break them up into sublevels\n    normalizedPages.forEach((pagePath)=>root.insert(pagePath));\n    // Smoosh will then sort those sublevels up to the point where you get the correct route definition priority\n    return root.smoosh();\n} //# sourceMappingURL=sorted-routes.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/sorted-routes.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js":
/*!****************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/utils.js ***!
  \****************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DecodeError: function() {\n        return DecodeError;\n    },\n    MiddlewareNotFoundError: function() {\n        return MiddlewareNotFoundError;\n    },\n    MissingStaticPage: function() {\n        return MissingStaticPage;\n    },\n    NormalizeError: function() {\n        return NormalizeError;\n    },\n    PageNotFoundError: function() {\n        return PageNotFoundError;\n    },\n    SP: function() {\n        return SP;\n    },\n    ST: function() {\n        return ST;\n    },\n    WEB_VITALS: function() {\n        return WEB_VITALS;\n    },\n    execOnce: function() {\n        return execOnce;\n    },\n    getDisplayName: function() {\n        return getDisplayName;\n    },\n    getLocationOrigin: function() {\n        return getLocationOrigin;\n    },\n    getURL: function() {\n        return getURL;\n    },\n    isAbsoluteUrl: function() {\n        return isAbsoluteUrl;\n    },\n    isResSent: function() {\n        return isResSent;\n    },\n    loadGetInitialProps: function() {\n        return loadGetInitialProps;\n    },\n    normalizeRepeatedSlashes: function() {\n        return normalizeRepeatedSlashes;\n    },\n    stringifyError: function() {\n        return stringifyError;\n    }\n});\nconst WEB_VITALS = [\n    \"CLS\",\n    \"FCP\",\n    \"FID\",\n    \"INP\",\n    \"LCP\",\n    \"TTFB\"\n];\nfunction execOnce(fn) {\n    let used = false;\n    let result;\n    return function() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        if (!used) {\n            used = true;\n            result = fn(...args);\n        }\n        return result;\n    };\n}\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/;\nconst isAbsoluteUrl = (url)=>ABSOLUTE_URL_REGEX.test(url);\nfunction getLocationOrigin() {\n    const { protocol, hostname, port } = window.location;\n    return protocol + \"//\" + hostname + (port ? \":\" + port : \"\");\n}\nfunction getURL() {\n    const { href } = window.location;\n    const origin = getLocationOrigin();\n    return href.substring(origin.length);\n}\nfunction getDisplayName(Component) {\n    return typeof Component === \"string\" ? Component : Component.displayName || Component.name || \"Unknown\";\n}\nfunction isResSent(res) {\n    return res.finished || res.headersSent;\n}\nfunction normalizeRepeatedSlashes(url) {\n    const urlParts = url.split(\"?\");\n    const urlNoQuery = urlParts[0];\n    return urlNoQuery // first we replace any non-encoded backslashes with forward\n    // then normalize repeated forward slashes\n    .replace(/\\\\/g, \"/\").replace(/\\/\\/+/g, \"/\") + (urlParts[1] ? \"?\" + urlParts.slice(1).join(\"?\") : \"\");\n}\nasync function loadGetInitialProps(App, ctx) {\n    if (true) {\n        var _App_prototype;\n        if ((_App_prototype = App.prototype) == null ? void 0 : _App_prototype.getInitialProps) {\n            const message = '\"' + getDisplayName(App) + '.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.';\n            throw new Error(message);\n        }\n    }\n    // when called from _app `ctx` is nested in `ctx`\n    const res = ctx.res || ctx.ctx && ctx.ctx.res;\n    if (!App.getInitialProps) {\n        if (ctx.ctx && ctx.Component) {\n            // @ts-ignore pageProps default\n            return {\n                pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx)\n            };\n        }\n        return {};\n    }\n    const props = await App.getInitialProps(ctx);\n    if (res && isResSent(res)) {\n        return props;\n    }\n    if (!props) {\n        const message = '\"' + getDisplayName(App) + '.getInitialProps()\" should resolve to an object. But found \"' + props + '\" instead.';\n        throw new Error(message);\n    }\n    if (true) {\n        if (Object.keys(props).length === 0 && !ctx.ctx) {\n            console.warn(\"\" + getDisplayName(App) + \" returned an empty object from `getInitialProps`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps\");\n        }\n    }\n    return props;\n}\nconst SP = typeof performance !== \"undefined\";\nconst ST = SP && [\n    \"mark\",\n    \"measure\",\n    \"getEntriesByName\"\n].every((method)=>typeof performance[method] === \"function\");\nclass DecodeError extends Error {\n}\nclass NormalizeError extends Error {\n}\nclass PageNotFoundError extends Error {\n    constructor(page){\n        super();\n        this.code = \"ENOENT\";\n        this.name = \"PageNotFoundError\";\n        this.message = \"Cannot find module for page: \" + page;\n    }\n}\nclass MissingStaticPage extends Error {\n    constructor(page, message){\n        super();\n        this.message = \"Failed to load static file for page: \" + page + \" \" + message;\n    }\n}\nclass MiddlewareNotFoundError extends Error {\n    constructor(){\n        super();\n        this.code = \"ENOENT\";\n        this.message = \"Cannot find the middleware module\";\n    }\n}\nfunction stringifyError(error) {\n    return JSON.stringify({\n        message: error.message,\n        stack: error.stack\n    });\n} //# sourceMappingURL=utils.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/context-map/page.tsx":
/*!**************************************!*\
  !*** ./src/app/context-map/page.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ContextMap; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ModuleLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ModuleLayout */ \"(app-pages-browser)/./src/components/ModuleLayout.tsx\");\n/* harmony import */ var _components_SmartQuestion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/SmartQuestion */ \"(app-pages-browser)/./src/components/SmartQuestion.tsx\");\n/* harmony import */ var _components_OutputPanel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/OutputPanel */ \"(app-pages-browser)/./src/components/OutputPanel.tsx\");\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ContextMap() {\n    _s();\n    const { contextMap, updateContextMap } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_4__.useContextStore)();\n    const questions = [\n        {\n            id: \"timeContext\",\n            question: \"What is the temporal context of your project?\",\n            questionAr: \"ما هو السياق الزمني لمشروعك؟\",\n            placeholder: \"e.g., Global 24/7 support, Business hours EST, Real-time responses...\",\n            placeholderAr: \"مثال: دعم عالمي على مدار الساعة، ساعات العمل بتوقيت شرق أمريكا، استجابات فورية...\",\n            aiSuggestion: \"Consider time zones, working hours, response time expectations, and any time-sensitive requirements.\",\n            aiSuggestionAr: \"فكر في المناطق الزمنية وساعات العمل وتوقعات وقت الاستجابة وأي متطلبات حساسة للوقت.\",\n            promptTemplate: 'Help me optimize this temporal context for an AI project: \"{answer}\". Suggest improvements for better time management.'\n        },\n        {\n            id: \"language\",\n            question: \"What languages should your AI system support?\",\n            questionAr: \"ما هي اللغات التي يجب أن يدعمها نظام الذكاء الاصطناعي؟\",\n            placeholder: \"e.g., English primary, Arabic secondary, Multilingual support...\",\n            placeholderAr: \"مثال: الإنجليزية أساسية، العربية ثانوية، دعم متعدد اللغات...\",\n            type: \"text\",\n            aiSuggestion: \"Consider your target audience, regional requirements, and the complexity of multilingual support.\",\n            aiSuggestionAr: \"فكر في جمهورك المستهدف والمتطلبات الإقليمية وتعقيد الدعم متعدد اللغات.\",\n            promptTemplate: 'Analyze this language requirement for an AI system: \"{answer}\". Suggest implementation strategies.'\n        },\n        {\n            id: \"location\",\n            question: \"What geographic regions or locations will this project serve?\",\n            questionAr: \"ما هي المناطق الجغرافية أو المواقع التي سيخدمها هذا المشروع؟\",\n            placeholder: \"e.g., Middle East, North America, Global, Specific cities...\",\n            placeholderAr: \"مثال: الشرق الأوسط، أمريكا الشمالية، عالمي، مدن محددة...\",\n            aiSuggestion: \"Think about regional regulations, cultural differences, and infrastructure requirements.\",\n            aiSuggestionAr: \"فكر في اللوائح الإقليمية والاختلافات الثقافية ومتطلبات البنية التحتية.\",\n            promptTemplate: 'Help me understand the geographic implications of this scope: \"{answer}\". What should I consider?'\n        }\n    ];\n    const handleFieldChange = (field, value)=>{\n        updateContextMap({\n            [field]: value\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ModuleLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        title: \"Context Map\",\n        titleAr: \"خريطة السياق\",\n        subtitle: \"Define the contextual framework for your AI project\",\n        subtitleAr: \"حدد الإطار السياقي لمشروع الذكاء الاصطناعي\",\n        emoji: \"\\uD83D\\uDDFA️\",\n        backLink: {\n            href: \"/project-definition\",\n            label: \"← Back to Project Definition\",\n            labelAr: \"← العودة لتعريف المشروع\"\n        },\n        nextLink: {\n            href: \"/emotional-tone\",\n            label: \"Next: Emotional Tone →\",\n            labelAr: \"التالي: النبرة العاطفية ←\"\n        },\n        rightPanel: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OutputPanel__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            moduleData: contextMap,\n            moduleName: \"Context Map\",\n            moduleNameAr: \"خريطة السياق\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n            lineNumber: 67,\n            columnNumber: 9\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: questions.map((question)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SmartQuestion__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    id: question.id,\n                    question: question.question,\n                    questionAr: question.questionAr,\n                    placeholder: question.placeholder,\n                    placeholderAr: question.placeholderAr,\n                    value: contextMap[question.id] || \"\",\n                    onChange: (value)=>handleFieldChange(question.id, value),\n                    type: question.type,\n                    aiSuggestion: question.aiSuggestion,\n                    aiSuggestionAr: question.aiSuggestionAr,\n                    promptTemplate: question.promptTemplate\n                }, question.id, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n_s(ContextMap, \"jlOHj64dV16jvKA34KKvBF1tiHE=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_4__.useContextStore\n    ];\n});\n_c = ContextMap;\nvar _c;\n$RefreshReg$(_c, \"ContextMap\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvY29udGV4dC1tYXAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFcUQ7QUFDRTtBQUNKO0FBQ0k7QUFFeEMsU0FBU0k7O0lBQ3RCLE1BQU0sRUFBRUMsVUFBVSxFQUFFQyxnQkFBZ0IsRUFBRSxHQUFHSCxvRUFBZUE7SUFFeEQsTUFBTUksWUFBWTtRQUNoQjtZQUNFQyxJQUFJO1lBQ0pDLFVBQVU7WUFDVkMsWUFBWTtZQUNaQyxhQUFhO1lBQ2JDLGVBQWU7WUFDZkMsY0FBYztZQUNkQyxnQkFBZ0I7WUFDaEJDLGdCQUFnQjtRQUNsQjtRQUNBO1lBQ0VQLElBQUk7WUFDSkMsVUFBVTtZQUNWQyxZQUFZO1lBQ1pDLGFBQWE7WUFDYkMsZUFBZTtZQUNmSSxNQUFNO1lBQ05ILGNBQWM7WUFDZEMsZ0JBQWdCO1lBQ2hCQyxnQkFBZ0I7UUFDbEI7UUFDQTtZQUNFUCxJQUFJO1lBQ0pDLFVBQVU7WUFDVkMsWUFBWTtZQUNaQyxhQUFhO1lBQ2JDLGVBQWU7WUFDZkMsY0FBYztZQUNkQyxnQkFBZ0I7WUFDaEJDLGdCQUFnQjtRQUNsQjtLQUNEO0lBRUQsTUFBTUUsb0JBQW9CLENBQUNDLE9BQWdDQztRQUN6RGIsaUJBQWlCO1lBQUUsQ0FBQ1ksTUFBTSxFQUFFQztRQUFNO0lBQ3BDO0lBRUEscUJBQ0UsOERBQUNuQixnRUFBWUE7UUFDWG9CLE9BQU07UUFDTkMsU0FBUTtRQUNSQyxVQUFTO1FBQ1RDLFlBQVc7UUFDWEMsT0FBTTtRQUNOQyxVQUFVO1lBQ1JDLE1BQU07WUFDTkMsT0FBTztZQUNQQyxTQUFTO1FBQ1g7UUFDQUMsVUFBVTtZQUNSSCxNQUFNO1lBQ05DLE9BQU87WUFDUEMsU0FBUztRQUNYO1FBQ0FFLDBCQUNFLDhEQUFDNUIsK0RBQVdBO1lBQ1Y2QixZQUFZMUI7WUFDWjJCLFlBQVc7WUFDWEMsY0FBYTs7Ozs7O2tCQUlqQiw0RUFBQ0M7WUFBSUMsV0FBVTtzQkFDWjVCLFVBQVU2QixHQUFHLENBQUMsQ0FBQzNCLHlCQUNkLDhEQUFDUixpRUFBYUE7b0JBRVpPLElBQUlDLFNBQVNELEVBQUU7b0JBQ2ZDLFVBQVVBLFNBQVNBLFFBQVE7b0JBQzNCQyxZQUFZRCxTQUFTQyxVQUFVO29CQUMvQkMsYUFBYUYsU0FBU0UsV0FBVztvQkFDakNDLGVBQWVILFNBQVNHLGFBQWE7b0JBQ3JDTyxPQUFPZCxVQUFVLENBQUNJLFNBQVNELEVBQUUsQ0FBNEIsSUFBSTtvQkFDN0Q2QixVQUFVLENBQUNsQixRQUFVRixrQkFBa0JSLFNBQVNELEVBQUUsRUFBNkJXO29CQUMvRUgsTUFBTVAsU0FBU08sSUFBSTtvQkFDbkJILGNBQWNKLFNBQVNJLFlBQVk7b0JBQ25DQyxnQkFBZ0JMLFNBQVNLLGNBQWM7b0JBQ3ZDQyxnQkFBZ0JOLFNBQVNNLGNBQWM7bUJBWGxDTixTQUFTRCxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7QUFpQjVCO0dBdEZ3Qko7O1FBQ21CRCxnRUFBZUE7OztLQURsQ0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2FwcC9jb250ZXh0LW1hcC9wYWdlLnRzeD80NzNmIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IE1vZHVsZUxheW91dCBmcm9tICdAL2NvbXBvbmVudHMvTW9kdWxlTGF5b3V0JztcbmltcG9ydCBTbWFydFF1ZXN0aW9uIGZyb20gJ0AvY29tcG9uZW50cy9TbWFydFF1ZXN0aW9uJztcbmltcG9ydCBPdXRwdXRQYW5lbCBmcm9tICdAL2NvbXBvbmVudHMvT3V0cHV0UGFuZWwnO1xuaW1wb3J0IHsgdXNlQ29udGV4dFN0b3JlIH0gZnJvbSAnQC9zdG9yZS9jb250ZXh0U3RvcmUnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBDb250ZXh0TWFwKCkge1xuICBjb25zdCB7IGNvbnRleHRNYXAsIHVwZGF0ZUNvbnRleHRNYXAgfSA9IHVzZUNvbnRleHRTdG9yZSgpO1xuXG4gIGNvbnN0IHF1ZXN0aW9ucyA9IFtcbiAgICB7XG4gICAgICBpZDogJ3RpbWVDb250ZXh0JyxcbiAgICAgIHF1ZXN0aW9uOiAnV2hhdCBpcyB0aGUgdGVtcG9yYWwgY29udGV4dCBvZiB5b3VyIHByb2plY3Q/JyxcbiAgICAgIHF1ZXN0aW9uQXI6ICfZhdinINmH2Ygg2KfZhNiz2YrYp9mCINin2YTYstmF2YbZiiDZhNmF2LTYsdmI2LnZg9ifJyxcbiAgICAgIHBsYWNlaG9sZGVyOiAnZS5nLiwgR2xvYmFsIDI0Lzcgc3VwcG9ydCwgQnVzaW5lc3MgaG91cnMgRVNULCBSZWFsLXRpbWUgcmVzcG9uc2VzLi4uJyxcbiAgICAgIHBsYWNlaG9sZGVyQXI6ICfZhdir2KfZhDog2K/YudmFINi52KfZhNmF2Yog2LnZhNmJINmF2K/Yp9ixINin2YTYs9in2LnYqdiMINiz2KfYudin2Kog2KfZhNi52YXZhCDYqNiq2YjZgtmK2Kog2LTYsdmCINij2YXYsdmK2YPYp9iMINin2LPYqtis2KfYqNin2Kog2YHZiNix2YrYqS4uLicsXG4gICAgICBhaVN1Z2dlc3Rpb246ICdDb25zaWRlciB0aW1lIHpvbmVzLCB3b3JraW5nIGhvdXJzLCByZXNwb25zZSB0aW1lIGV4cGVjdGF0aW9ucywgYW5kIGFueSB0aW1lLXNlbnNpdGl2ZSByZXF1aXJlbWVudHMuJyxcbiAgICAgIGFpU3VnZ2VzdGlvbkFyOiAn2YHZg9ixINmB2Yog2KfZhNmF2YbYp9i32YIg2KfZhNiy2YXZhtmK2Kkg2YjYs9in2LnYp9iqINin2YTYudmF2YQg2YjYqtmI2YLYudin2Kog2YjZgtiqINin2YTYp9iz2KrYrNin2KjYqSDZiNij2Yog2YXYqti32YTYqNin2Kog2K3Ys9in2LPYqSDZhNmE2YjZgtiqLicsXG4gICAgICBwcm9tcHRUZW1wbGF0ZTogJ0hlbHAgbWUgb3B0aW1pemUgdGhpcyB0ZW1wb3JhbCBjb250ZXh0IGZvciBhbiBBSSBwcm9qZWN0OiBcInthbnN3ZXJ9XCIuIFN1Z2dlc3QgaW1wcm92ZW1lbnRzIGZvciBiZXR0ZXIgdGltZSBtYW5hZ2VtZW50LidcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiAnbGFuZ3VhZ2UnLFxuICAgICAgcXVlc3Rpb246ICdXaGF0IGxhbmd1YWdlcyBzaG91bGQgeW91ciBBSSBzeXN0ZW0gc3VwcG9ydD8nLFxuICAgICAgcXVlc3Rpb25BcjogJ9mF2Kcg2YfZiiDYp9mE2YTYutin2Kog2KfZhNiq2Yog2YrYrNioINij2YYg2YrYr9i52YXZh9inINmG2LjYp9mFINin2YTYsNmD2KfYoSDYp9mE2KfYtdi32YbYp9i52YrYnycsXG4gICAgICBwbGFjZWhvbGRlcjogJ2UuZy4sIEVuZ2xpc2ggcHJpbWFyeSwgQXJhYmljIHNlY29uZGFyeSwgTXVsdGlsaW5ndWFsIHN1cHBvcnQuLi4nLFxuICAgICAgcGxhY2Vob2xkZXJBcjogJ9mF2KvYp9mEOiDYp9mE2KXZhtis2YTZitiy2YrYqSDYo9iz2KfYs9mK2KnYjCDYp9mE2LnYsdio2YrYqSDYq9in2YbZiNmK2KnYjCDYr9i52YUg2YXYqti52K/YryDYp9mE2YTYutin2KouLi4nLFxuICAgICAgdHlwZTogJ3RleHQnIGFzIGNvbnN0LFxuICAgICAgYWlTdWdnZXN0aW9uOiAnQ29uc2lkZXIgeW91ciB0YXJnZXQgYXVkaWVuY2UsIHJlZ2lvbmFsIHJlcXVpcmVtZW50cywgYW5kIHRoZSBjb21wbGV4aXR5IG9mIG11bHRpbGluZ3VhbCBzdXBwb3J0LicsXG4gICAgICBhaVN1Z2dlc3Rpb25BcjogJ9mB2YPYsSDZgdmKINis2YXZh9mI2LHZgyDYp9mE2YXYs9iq2YfYr9mBINmI2KfZhNmF2KrYt9mE2KjYp9iqINin2YTYpdmC2YTZitmF2YrYqSDZiNiq2LnZgtmK2K8g2KfZhNiv2LnZhSDZhdiq2LnYr9ivINin2YTZhNi62KfYqi4nLFxuICAgICAgcHJvbXB0VGVtcGxhdGU6ICdBbmFseXplIHRoaXMgbGFuZ3VhZ2UgcmVxdWlyZW1lbnQgZm9yIGFuIEFJIHN5c3RlbTogXCJ7YW5zd2VyfVwiLiBTdWdnZXN0IGltcGxlbWVudGF0aW9uIHN0cmF0ZWdpZXMuJ1xuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICdsb2NhdGlvbicsXG4gICAgICBxdWVzdGlvbjogJ1doYXQgZ2VvZ3JhcGhpYyByZWdpb25zIG9yIGxvY2F0aW9ucyB3aWxsIHRoaXMgcHJvamVjdCBzZXJ2ZT8nLFxuICAgICAgcXVlc3Rpb25BcjogJ9mF2Kcg2YfZiiDYp9mE2YXZhtin2LfZgiDYp9mE2KzYutix2KfZgdmK2Kkg2KPZiCDYp9mE2YXZiNin2YLYuSDYp9mE2KrZiiDYs9mK2K7Yr9mF2YfYpyDZh9iw2Kcg2KfZhNmF2LTYsdmI2LnYnycsXG4gICAgICBwbGFjZWhvbGRlcjogJ2UuZy4sIE1pZGRsZSBFYXN0LCBOb3J0aCBBbWVyaWNhLCBHbG9iYWwsIFNwZWNpZmljIGNpdGllcy4uLicsXG4gICAgICBwbGFjZWhvbGRlckFyOiAn2YXYq9in2YQ6INin2YTYtNix2YIg2KfZhNij2YjYs9i32Iwg2KPZhdix2YrZg9inINin2YTYtNmF2KfZhNmK2KnYjCDYudin2YTZhdmK2Iwg2YXYr9mGINmF2K3Yr9iv2KkuLi4nLFxuICAgICAgYWlTdWdnZXN0aW9uOiAnVGhpbmsgYWJvdXQgcmVnaW9uYWwgcmVndWxhdGlvbnMsIGN1bHR1cmFsIGRpZmZlcmVuY2VzLCBhbmQgaW5mcmFzdHJ1Y3R1cmUgcmVxdWlyZW1lbnRzLicsXG4gICAgICBhaVN1Z2dlc3Rpb25BcjogJ9mB2YPYsSDZgdmKINin2YTZhNmI2KfYptitINin2YTYpdmC2YTZitmF2YrYqSDZiNin2YTYp9iu2KrZhNin2YHYp9iqINin2YTYq9mC2KfZgdmK2Kkg2YjZhdiq2LfZhNio2KfYqiDYp9mE2KjZhtmK2Kkg2KfZhNiq2K3YqtmK2KkuJyxcbiAgICAgIHByb21wdFRlbXBsYXRlOiAnSGVscCBtZSB1bmRlcnN0YW5kIHRoZSBnZW9ncmFwaGljIGltcGxpY2F0aW9ucyBvZiB0aGlzIHNjb3BlOiBcInthbnN3ZXJ9XCIuIFdoYXQgc2hvdWxkIEkgY29uc2lkZXI/J1xuICAgIH1cbiAgXTtcblxuICBjb25zdCBoYW5kbGVGaWVsZENoYW5nZSA9IChmaWVsZDoga2V5b2YgdHlwZW9mIGNvbnRleHRNYXAsIHZhbHVlOiBzdHJpbmcpID0+IHtcbiAgICB1cGRhdGVDb250ZXh0TWFwKHsgW2ZpZWxkXTogdmFsdWUgfSk7XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8TW9kdWxlTGF5b3V0XG4gICAgICB0aXRsZT1cIkNvbnRleHQgTWFwXCJcbiAgICAgIHRpdGxlQXI9XCLYrtix2YrYt9ipINin2YTYs9mK2KfZglwiXG4gICAgICBzdWJ0aXRsZT1cIkRlZmluZSB0aGUgY29udGV4dHVhbCBmcmFtZXdvcmsgZm9yIHlvdXIgQUkgcHJvamVjdFwiXG4gICAgICBzdWJ0aXRsZUFyPVwi2K3Yr9ivINin2YTYpdi32KfYsSDYp9mE2LPZitin2YLZiiDZhNmF2LTYsdmI2Lkg2KfZhNiw2YPYp9ihINin2YTYp9i12LfZhtin2LnZilwiXG4gICAgICBlbW9qaT1cIvCfl7rvuI9cIlxuICAgICAgYmFja0xpbms9e3tcbiAgICAgICAgaHJlZjogXCIvcHJvamVjdC1kZWZpbml0aW9uXCIsXG4gICAgICAgIGxhYmVsOiBcIuKGkCBCYWNrIHRvIFByb2plY3QgRGVmaW5pdGlvblwiLFxuICAgICAgICBsYWJlbEFyOiBcIuKGkCDYp9mE2LnZiNiv2Kkg2YTYqti52LHZitmBINin2YTZhdi02LHZiNi5XCJcbiAgICAgIH19XG4gICAgICBuZXh0TGluaz17e1xuICAgICAgICBocmVmOiBcIi9lbW90aW9uYWwtdG9uZVwiLFxuICAgICAgICBsYWJlbDogXCJOZXh0OiBFbW90aW9uYWwgVG9uZSDihpJcIixcbiAgICAgICAgbGFiZWxBcjogXCLYp9mE2KrYp9mE2Yo6INin2YTZhtio2LHYqSDYp9mE2LnYp9i32YHZitipIOKGkFwiXG4gICAgICB9fVxuICAgICAgcmlnaHRQYW5lbD17XG4gICAgICAgIDxPdXRwdXRQYW5lbFxuICAgICAgICAgIG1vZHVsZURhdGE9e2NvbnRleHRNYXB9XG4gICAgICAgICAgbW9kdWxlTmFtZT1cIkNvbnRleHQgTWFwXCJcbiAgICAgICAgICBtb2R1bGVOYW1lQXI9XCLYrtix2YrYt9ipINin2YTYs9mK2KfZglwiXG4gICAgICAgIC8+XG4gICAgICB9XG4gICAgPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAge3F1ZXN0aW9ucy5tYXAoKHF1ZXN0aW9uKSA9PiAoXG4gICAgICAgICAgPFNtYXJ0UXVlc3Rpb25cbiAgICAgICAgICAgIGtleT17cXVlc3Rpb24uaWR9XG4gICAgICAgICAgICBpZD17cXVlc3Rpb24uaWR9XG4gICAgICAgICAgICBxdWVzdGlvbj17cXVlc3Rpb24ucXVlc3Rpb259XG4gICAgICAgICAgICBxdWVzdGlvbkFyPXtxdWVzdGlvbi5xdWVzdGlvbkFyfVxuICAgICAgICAgICAgcGxhY2Vob2xkZXI9e3F1ZXN0aW9uLnBsYWNlaG9sZGVyfVxuICAgICAgICAgICAgcGxhY2Vob2xkZXJBcj17cXVlc3Rpb24ucGxhY2Vob2xkZXJBcn1cbiAgICAgICAgICAgIHZhbHVlPXtjb250ZXh0TWFwW3F1ZXN0aW9uLmlkIGFzIGtleW9mIHR5cGVvZiBjb250ZXh0TWFwXSB8fCAnJ31cbiAgICAgICAgICAgIG9uQ2hhbmdlPXsodmFsdWUpID0+IGhhbmRsZUZpZWxkQ2hhbmdlKHF1ZXN0aW9uLmlkIGFzIGtleW9mIHR5cGVvZiBjb250ZXh0TWFwLCB2YWx1ZSl9XG4gICAgICAgICAgICB0eXBlPXtxdWVzdGlvbi50eXBlfVxuICAgICAgICAgICAgYWlTdWdnZXN0aW9uPXtxdWVzdGlvbi5haVN1Z2dlc3Rpb259XG4gICAgICAgICAgICBhaVN1Z2dlc3Rpb25Bcj17cXVlc3Rpb24uYWlTdWdnZXN0aW9uQXJ9XG4gICAgICAgICAgICBwcm9tcHRUZW1wbGF0ZT17cXVlc3Rpb24ucHJvbXB0VGVtcGxhdGV9XG4gICAgICAgICAgLz5cbiAgICAgICAgKSl9XG4gICAgICA8L2Rpdj5cbiAgICA8L01vZHVsZUxheW91dD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJNb2R1bGVMYXlvdXQiLCJTbWFydFF1ZXN0aW9uIiwiT3V0cHV0UGFuZWwiLCJ1c2VDb250ZXh0U3RvcmUiLCJDb250ZXh0TWFwIiwiY29udGV4dE1hcCIsInVwZGF0ZUNvbnRleHRNYXAiLCJxdWVzdGlvbnMiLCJpZCIsInF1ZXN0aW9uIiwicXVlc3Rpb25BciIsInBsYWNlaG9sZGVyIiwicGxhY2Vob2xkZXJBciIsImFpU3VnZ2VzdGlvbiIsImFpU3VnZ2VzdGlvbkFyIiwicHJvbXB0VGVtcGxhdGUiLCJ0eXBlIiwiaGFuZGxlRmllbGRDaGFuZ2UiLCJmaWVsZCIsInZhbHVlIiwidGl0bGUiLCJ0aXRsZUFyIiwic3VidGl0bGUiLCJzdWJ0aXRsZUFyIiwiZW1vamkiLCJiYWNrTGluayIsImhyZWYiLCJsYWJlbCIsImxhYmVsQXIiLCJuZXh0TGluayIsInJpZ2h0UGFuZWwiLCJtb2R1bGVEYXRhIiwibW9kdWxlTmFtZSIsIm1vZHVsZU5hbWVBciIsImRpdiIsImNsYXNzTmFtZSIsIm1hcCIsIm9uQ2hhbmdlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/context-map/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _ThemeToggle__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ThemeToggle */ \"(app-pages-browser)/./src/components/ThemeToggle.tsx\");\n/* harmony import */ var _LanguageToggle__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./LanguageToggle */ \"(app-pages-browser)/./src/components/LanguageToggle.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Header(param) {\n    let { title, subtitle, backLink, emoji } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-4 right-4 z-50 flex space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-lg shadow-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LanguageToggle__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-lg shadow-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ThemeToggle__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-8 pt-4\",\n                children: [\n                    backLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        href: backLink.href,\n                        className: \"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mb-4 inline-block transition-colors\",\n                        children: backLink.label\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-2\",\n                        children: [\n                            emoji && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-2\",\n                                children: emoji\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 21\n                            }, this),\n                            title\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto\",\n                        children: subtitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n_c = Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Header.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/LanguageToggle.tsx":
/*!*******************************************!*\
  !*** ./src/components/LanguageToggle.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LanguageToggle; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction LanguageToggle() {\n    _s();\n    const { currentLanguage, setLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_1__.useContextStore)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: ()=>setLanguage(currentLanguage === \"ar\" ? \"en\" : \"ar\"),\n        className: \"flex items-center px-3 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors text-sm font-medium\",\n        title: currentLanguage === \"ar\" ? \"Switch to English\" : \"التبديل إلى العربية\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"mr-2\",\n                children: \"\\uD83C\\uDF10\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            currentLanguage === \"ar\" ? \"EN\" : \"عر\"\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n_s(LanguageToggle, \"a9M9cHmCao4il6jcKdOx4zCwv7c=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_1__.useContextStore\n    ];\n});\n_c = LanguageToggle;\nvar _c;\n$RefreshReg$(_c, \"LanguageToggle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0xhbmd1YWdlVG9nZ2xlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUV1RDtBQUV4QyxTQUFTQzs7SUFDdEIsTUFBTSxFQUFFQyxlQUFlLEVBQUVDLFdBQVcsRUFBRSxHQUFHSCxvRUFBZUE7SUFFeEQscUJBQ0UsOERBQUNJO1FBQ0NDLFNBQVMsSUFBTUYsWUFBWUQsb0JBQW9CLE9BQU8sT0FBTztRQUM3REksV0FBVTtRQUNWQyxPQUFPTCxvQkFBb0IsT0FBTyxzQkFBc0I7OzBCQUV4RCw4REFBQ007Z0JBQUtGLFdBQVU7MEJBQU87Ozs7OztZQUN0Qkosb0JBQW9CLE9BQU8sT0FBTzs7Ozs7OztBQUd6QztHQWJ3QkQ7O1FBQ21CRCxnRUFBZUE7OztLQURsQ0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvTGFuZ3VhZ2VUb2dnbGUudHN4PzEzODAiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VDb250ZXh0U3RvcmUgfSBmcm9tICdAL3N0b3JlL2NvbnRleHRTdG9yZSc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExhbmd1YWdlVG9nZ2xlKCkge1xuICBjb25zdCB7IGN1cnJlbnRMYW5ndWFnZSwgc2V0TGFuZ3VhZ2UgfSA9IHVzZUNvbnRleHRTdG9yZSgpO1xuXG4gIHJldHVybiAoXG4gICAgPGJ1dHRvblxuICAgICAgb25DbGljaz17KCkgPT4gc2V0TGFuZ3VhZ2UoY3VycmVudExhbmd1YWdlID09PSAnYXInID8gJ2VuJyA6ICdhcicpfVxuICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgcHgtMyBweS0yIGJnLWdyYXktMTAwIGRhcms6YmctZ3JheS03MDAgaG92ZXI6YmctZ3JheS0yMDAgZGFyazpob3ZlcjpiZy1ncmF5LTYwMCByb3VuZGVkLWxnIHRyYW5zaXRpb24tY29sb3JzIHRleHQtc20gZm9udC1tZWRpdW1cIlxuICAgICAgdGl0bGU9e2N1cnJlbnRMYW5ndWFnZSA9PT0gJ2FyJyA/ICdTd2l0Y2ggdG8gRW5nbGlzaCcgOiAn2KfZhNiq2KjYr9mK2YQg2KXZhNmJINin2YTYudix2KjZitipJ31cbiAgICA+XG4gICAgICA8c3BhbiBjbGFzc05hbWU9XCJtci0yXCI+8J+MkDwvc3Bhbj5cbiAgICAgIHtjdXJyZW50TGFuZ3VhZ2UgPT09ICdhcicgPyAnRU4nIDogJ9i52LEnfVxuICAgIDwvYnV0dG9uPlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZUNvbnRleHRTdG9yZSIsIkxhbmd1YWdlVG9nZ2xlIiwiY3VycmVudExhbmd1YWdlIiwic2V0TGFuZ3VhZ2UiLCJidXR0b24iLCJvbkNsaWNrIiwiY2xhc3NOYW1lIiwidGl0bGUiLCJzcGFuIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/LanguageToggle.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ModuleLayout.tsx":
/*!*****************************************!*\
  !*** ./src/components/ModuleLayout.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ModuleLayout; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Header */ \"(app-pages-browser)/./src/components/Header.tsx\");\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction ModuleLayout(param) {\n    let { title, titleAr, subtitle, subtitleAr, emoji, backLink, nextLink, children, rightPanel } = param;\n    _s();\n    const { currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    title: isArabic ? titleAr : title,\n                    subtitle: isArabic ? subtitleAr : subtitle,\n                    emoji: emoji,\n                    backLink: backLink ? {\n                        href: backLink.href,\n                        label: isArabic ? backLink.labelAr : backLink.label\n                    } : undefined\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-2 gap-8 max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6 \".concat(isArabic ? \"lg:order-2\" : \"lg:order-1\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl mr-3\",\n                                                children: \"✍️\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                lineNumber: 61,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                                                children: isArabic ? \"الأسئلة الذكية\" : \"Smart Questions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                lineNumber: 62,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 15\n                                    }, this),\n                                    children\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6 \".concat(isArabic ? \"lg:order-1\" : \"lg:order-2\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 sticky top-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl mr-3\",\n                                                    children: \"\\uD83D\\uDCC4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                    lineNumber: 75,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                                                    children: isArabic ? \"المخرجات المجمّعة\" : \"Generated Outputs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                    lineNumber: 76,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 15\n                                    }, this),\n                                    rightPanel\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this),\n                (backLink || nextLink) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mt-12 max-w-7xl mx-auto\",\n                    children: [\n                        backLink ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: backLink.href,\n                            className: \"flex items-center px-6 py-3 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"mr-2\",\n                                    children: \"←\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 17\n                                }, this),\n                                isArabic ? backLink.labelAr : backLink.label\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 15\n                        }, this),\n                        nextLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: nextLink.href,\n                            className: \"flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors\",\n                            children: [\n                                isArabic ? nextLink.labelAr : nextLink.label,\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2\",\n                                    children: \"→\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n            lineNumber: 43,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n_s(ModuleLayout, \"TUcpdwZ+GxByrtxz7lNUh3/XmDk=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore\n    ];\n});\n_c = ModuleLayout;\nvar _c;\n$RefreshReg$(_c, \"ModuleLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ModuleLayout.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/OutputPanel.tsx":
/*!****************************************!*\
  !*** ./src/components/OutputPanel.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ OutputPanel; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction OutputPanel(param) {\n    let { moduleData, moduleName, moduleNameAr } = param;\n    _s();\n    const { currentLanguage, outputFormat, setOutputFormat } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore)();\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isArabic = currentLanguage === \"ar\";\n    // تحويل البيانات إلى تنسيقات مختلفة\n    const generateMarkdown = ()=>{\n        const title = isArabic ? moduleNameAr : moduleName;\n        let markdown = \"# \".concat(title, \"\\n\\n\");\n        Object.entries(moduleData).forEach((param)=>{\n            let [key, value] = param;\n            if (value && typeof value === \"string\" && value.trim()) {\n                const formattedKey = key.replace(/([A-Z])/g, \" $1\").replace(/^./, (str)=>str.toUpperCase());\n                markdown += \"## \".concat(formattedKey, \"\\n\").concat(value, \"\\n\\n\");\n            }\n        });\n        return markdown;\n    };\n    const generateHTML = ()=>{\n        const title = isArabic ? moduleNameAr : moduleName;\n        let html = '<div class=\"module-output\">\\n  <h1>'.concat(title, \"</h1>\\n\");\n        Object.entries(moduleData).forEach((param)=>{\n            let [key, value] = param;\n            if (value && typeof value === \"string\" && value.trim()) {\n                const formattedKey = key.replace(/([A-Z])/g, \" $1\").replace(/^./, (str)=>str.toUpperCase());\n                html += \"  <section>\\n    <h2>\".concat(formattedKey, \"</h2>\\n    <p>\").concat(value, \"</p>\\n  </section>\\n\");\n            }\n        });\n        html += \"</div>\";\n        return html;\n    };\n    const generateJSON = ()=>{\n        const filteredData = Object.fromEntries(Object.entries(moduleData).filter((param)=>{\n            let [_, value] = param;\n            return value && typeof value === \"string\" && value.trim();\n        }));\n        return JSON.stringify({\n            module: isArabic ? moduleNameAr : moduleName,\n            data: filteredData,\n            timestamp: new Date().toISOString()\n        }, null, 2);\n    };\n    const getCurrentOutput = ()=>{\n        switch(outputFormat){\n            case \"markdown\":\n                return generateMarkdown();\n            case \"html\":\n                return generateHTML();\n            case \"json\":\n                return generateJSON();\n            default:\n                return generateMarkdown();\n        }\n    };\n    const handleCopyAll = async ()=>{\n        const output = getCurrentOutput();\n        await navigator.clipboard.writeText(output);\n        setCopied(true);\n        setTimeout(()=>setCopied(false), 2000);\n    };\n    const handleDownload = ()=>{\n        const output = getCurrentOutput();\n        const extension = outputFormat === \"json\" ? \"json\" : outputFormat === \"html\" ? \"html\" : \"md\";\n        const filename = \"\".concat(moduleName.toLowerCase().replace(/\\s+/g, \"-\"), \".\").concat(extension);\n        const blob = new Blob([\n            output\n        ], {\n            type: \"text/plain\"\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = filename;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n    };\n    const hasData = Object.values(moduleData).some((value)=>value && typeof value === \"string\" && value.trim());\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: [\n                            \"markdown\",\n                            \"html\",\n                            \"json\"\n                        ].map((format)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setOutputFormat(format),\n                                className: \"px-3 py-1 text-sm rounded-lg transition-colors \".concat(outputFormat === format ? \"bg-blue-600 text-white\" : \"bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600\"),\n                                children: format.toUpperCase()\n                            }, format, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this),\n                    hasData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleCopyAll,\n                                className: \"flex items-center px-3 py-1 text-sm bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded-lg hover:bg-green-200 dark:hover:bg-green-900/30 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-1\",\n                                        children: \"\\uD83D\\uDCCB\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, this),\n                                    copied ? isArabic ? \"تم النسخ!\" : \"Copied!\" : isArabic ? \"نسخ الكل\" : \"Copy All\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleDownload,\n                                className: \"flex items-center px-3 py-1 text-sm bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-900/30 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-1\",\n                                        children: \"\\uD83D\\uDCBE\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this),\n                                    isArabic ? \"تحميل\" : \"Download\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 dark:bg-gray-900 rounded-lg p-4 min-h-[300px]\",\n                children: hasData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                    className: \"text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap font-mono overflow-x-auto \".concat(isArabic ? \"text-right\" : \"text-left\"),\n                    children: getCurrentOutput()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full text-gray-500 dark:text-gray-400\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-4xl mb-2 block\",\n                                children: \"\\uD83D\\uDCDD\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: isArabic ? \"ابدأ بالإجابة على الأسئلة لرؤية المخرجات\" : \"Start answering questions to see outputs\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                children: [\n                    outputFormat === \"markdown\" && (isArabic ? \"تنسيق Markdown - جاهز للاستخدام في المستندات\" : \"Markdown format - Ready for documentation\"),\n                    outputFormat === \"html\" && (isArabic ? \"تنسيق HTML - جاهز للمواقع الإلكترونية\" : \"HTML format - Ready for websites\"),\n                    outputFormat === \"json\" && (isArabic ? \"تنسيق JSON - جاهز للبرمجة والـ APIs\" : \"JSON format - Ready for programming and APIs\")\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, this);\n}\n_s(OutputPanel, \"sKb4eUZ0iLx4SneSpJIkONUkK08=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore\n    ];\n});\n_c = OutputPanel;\nvar _c;\n$RefreshReg$(_c, \"OutputPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OutputPanel.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/SmartQuestion.tsx":
/*!******************************************!*\
  !*** ./src/components/SmartQuestion.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SmartQuestion; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction SmartQuestion(param) {\n    let { id, question, questionAr, placeholder, placeholderAr, value, onChange, type = \"textarea\", aiSuggestion, aiSuggestionAr, promptTemplate } = param;\n    _s();\n    const { currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore)();\n    const [showSuggestion, setShowSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isArabic = currentLanguage === \"ar\";\n    const handleCopy = async ()=>{\n        if (value.trim()) {\n            await navigator.clipboard.writeText(value);\n            setCopied(true);\n            setTimeout(()=>setCopied(false), 2000);\n        }\n    };\n    const handlePromptCopy = async ()=>{\n        if (promptTemplate && value.trim()) {\n            const prompt = promptTemplate.replace(\"{answer}\", value);\n            await navigator.clipboard.writeText(prompt);\n            setCopied(true);\n            setTimeout(()=>setCopied(false), 2000);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4 p-4 border border-gray-200 dark:border-gray-600 rounded-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 dark:text-white mb-2\",\n                                children: isArabic ? questionAr : question\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 11\n                            }, this),\n                            (aiSuggestion || aiSuggestionAr) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowSuggestion(!showSuggestion),\n                                className: \"text-sm text-blue-600 dark:text-blue-400 hover:underline flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-1\",\n                                        children: \"\\uD83E\\uDDE0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 15\n                                    }, this),\n                                    isArabic ? \"عرض الاقتراح الذكي\" : \"Show AI Suggestion\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    value.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleCopy,\n                        className: \"flex items-center px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-1\",\n                                children: \"\\uD83D\\uDCCE\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 13\n                            }, this),\n                            copied ? isArabic ? \"تم النسخ!\" : \"Copied!\" : isArabic ? \"نسخ\" : \"Copy\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            showSuggestion && (aiSuggestion || aiSuggestionAr) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-blue-500 mr-2\",\n                            children: \"\\uD83D\\uDCA1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-blue-800 dark:text-blue-200\",\n                            children: isArabic ? aiSuggestionAr : aiSuggestion\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                lineNumber: 90,\n                columnNumber: 9\n            }, this),\n            type === \"textarea\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                value: value,\n                onChange: (e)=>onChange(e.target.value),\n                placeholder: isArabic ? placeholderAr : placeholder,\n                className: \"w-full p-4 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white resize-none \".concat(isArabic ? \"text-right\" : \"text-left\"),\n                rows: 4,\n                dir: isArabic ? \"rtl\" : \"ltr\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                lineNumber: 102,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                type: \"text\",\n                value: value,\n                onChange: (e)=>onChange(e.target.value),\n                placeholder: isArabic ? placeholderAr : placeholder,\n                className: \"w-full p-4 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white \".concat(isArabic ? \"text-right\" : \"text-left\"),\n                dir: isArabic ? \"rtl\" : \"ltr\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                lineNumber: 113,\n                columnNumber: 9\n            }, this),\n            promptTemplate && value.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handlePromptCopy,\n                    className: \"flex items-center px-4 py-2 text-sm bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded-lg hover:bg-green-200 dark:hover:bg-green-900/30 transition-colors\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"mr-1\",\n                            children: \"\\uD83D\\uDE80\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 13\n                        }, this),\n                        isArabic ? \"نسخ كـ Prompt\" : \"Copy as Prompt\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                lineNumber: 127,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n_s(SmartQuestion, \"23gELrRwHOSGqDEIjyy5y28Fmsk=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore\n    ];\n});\n_c = SmartQuestion;\nvar _c;\n$RefreshReg$(_c, \"SmartQuestion\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/SmartQuestion.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ThemeProvider.tsx":
/*!******************************************!*\
  !*** ./src/components/ThemeProvider.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: function() { return /* binding */ ThemeProvider; },\n/* harmony export */   useTheme: function() { return /* binding */ useTheme; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ThemeProvider(param) {\n    let { children } = param;\n    _s();\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"light\");\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize theme from localStorage or system preference\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedTheme = localStorage.getItem(\"theme\");\n        const systemTheme = window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\";\n        const initialTheme = savedTheme || systemTheme;\n        setTheme(initialTheme);\n        setMounted(true);\n    }, []);\n    // Apply theme to document\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (mounted) {\n            const root = document.documentElement;\n            if (theme === \"dark\") {\n                root.classList.add(\"dark\");\n            } else {\n                root.classList.remove(\"dark\");\n            }\n            localStorage.setItem(\"theme\", theme);\n        }\n    }, [\n        theme,\n        mounted\n    ]);\n    const toggleTheme = ()=>{\n        setTheme((prev)=>prev === \"light\" ? \"dark\" : \"light\");\n    };\n    // Prevent hydration mismatch\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: {\n            theme,\n            toggleTheme\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeProvider.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n_s(ThemeProvider, \"irO646EbSVqPL90dedilwyEs6oc=\");\n_c = ThemeProvider;\nfunction useTheme() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        // Return default values instead of throwing error during SSR\n        return {\n            theme: \"light\",\n            toggleTheme: ()=>{}\n        };\n    }\n    return context;\n}\n_s1(useTheme, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"ThemeProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ThemeProvider.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ThemeToggle.tsx":
/*!****************************************!*\
  !*** ./src/components/ThemeToggle.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ThemeToggle; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _ThemeProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ThemeProvider */ \"(app-pages-browser)/./src/components/ThemeProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction ThemeToggle() {\n    _s();\n    const { theme, toggleTheme } = (0,_ThemeProvider__WEBPACK_IMPORTED_MODULE_1__.useTheme)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: toggleTheme,\n        className: \"p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200 hover:scale-105 active:scale-95\",\n        \"aria-label\": \"Toggle theme\",\n        title: \"Switch to \".concat(theme === \"light\" ? \"dark\" : \"light\", \" mode\"),\n        children: theme === \"light\" ? // Moon icon for dark mode\n        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6 text-gray-600 dark:text-gray-400 transition-colors\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 24,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n            lineNumber: 17,\n            columnNumber: 9\n        }, this) : // Sun icon for light mode\n        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6 text-gray-600 dark:text-gray-400 transition-colors\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 40,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n            lineNumber: 33,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n_s(ThemeToggle, \"Q4eAjrIZ0CuRuhycs6byifK2KBk=\", false, function() {\n    return [\n        _ThemeProvider__WEBPACK_IMPORTED_MODULE_1__.useTheme\n    ];\n});\n_c = ThemeToggle;\nvar _c;\n$RefreshReg$(_c, \"ThemeToggle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ThemeToggle.tsx\n"));

/***/ })

});