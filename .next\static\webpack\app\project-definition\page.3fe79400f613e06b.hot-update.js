"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/project-definition/page",{

/***/ "(app-pages-browser)/./src/components/ModuleLayout.tsx":
/*!*****************************************!*\
  !*** ./src/components/ModuleLayout.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ModuleLayout; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Header */ \"(app-pages-browser)/./src/components/Header.tsx\");\n/* harmony import */ var _ProgressIndicator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ProgressIndicator */ \"(app-pages-browser)/./src/components/ProgressIndicator.tsx\");\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ModuleLayout(param) {\n    let { title, titleAr, subtitle, subtitleAr, emoji, moduleKey, backLink, nextLink, children, rightPanel } = param;\n    _s();\n    const { currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_3__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    title: isArabic ? titleAr : title,\n                    subtitle: isArabic ? subtitleAr : subtitle,\n                    emoji: emoji,\n                    backLink: backLink ? {\n                        href: backLink.href,\n                        label: isArabic ? backLink.labelAr : backLink.label\n                    } : undefined\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProgressIndicator__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    currentModule: moduleKey\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-2 gap-8 max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6 \".concat(isArabic ? \"lg:order-2\" : \"lg:order-1\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl mr-3\",\n                                                children: \"✍️\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                                                children: isArabic ? \"الأسئلة الذكية\" : \"Smart Questions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 15\n                                    }, this),\n                                    children\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6 \".concat(isArabic ? \"lg:order-1\" : \"lg:order-2\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 sticky top-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl mr-3\",\n                                                    children: \"\\uD83D\\uDCC4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                                                    children: isArabic ? \"المخرجات المجمّعة\" : \"Generated Outputs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, this),\n                                    rightPanel\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this),\n                (backLink || nextLink) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mt-12 max-w-7xl mx-auto\",\n                    children: [\n                        backLink ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: backLink.href,\n                            className: \"flex items-center px-6 py-3 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"mr-2\",\n                                    children: \"←\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 17\n                                }, this),\n                                isArabic ? backLink.labelAr : backLink.label\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 15\n                        }, this),\n                        nextLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: nextLink.href,\n                            className: \"flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors\",\n                            children: [\n                                isArabic ? nextLink.labelAr : nextLink.label,\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2\",\n                                    children: \"→\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n_s(ModuleLayout, \"TUcpdwZ+GxByrtxz7lNUh3/XmDk=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_3__.useContextStore\n    ];\n});\n_c = ModuleLayout;\nvar _c;\n$RefreshReg$(_c, \"ModuleLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ModuleLayout.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ProgressIndicator.tsx":
/*!**********************************************!*\
  !*** ./src/components/ProgressIndicator.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProgressIndicator; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction ProgressIndicator(param) {\n    let { currentModule } = param;\n    _s();\n    const { projectDefinition, contextMap, emotionalTone, technicalLayer, legalRisk, currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_1__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    const modules = [\n        {\n            key: \"project-definition\",\n            name: \"Project Definition\",\n            nameAr: \"تعريف المشروع\",\n            emoji: \"\\uD83C\\uDFAF\",\n            data: projectDefinition,\n            href: \"/project-definition\"\n        },\n        {\n            key: \"context-map\",\n            name: \"Context Map\",\n            nameAr: \"خريطة السياق\",\n            emoji: \"\\uD83D\\uDDFA️\",\n            data: contextMap,\n            href: \"/context-map\"\n        },\n        {\n            key: \"emotional-tone\",\n            name: \"Emotional Tone\",\n            nameAr: \"النبرة العاطفية\",\n            emoji: \"✨\",\n            data: emotionalTone,\n            href: \"/emotional-tone\"\n        },\n        {\n            key: \"technical-layer\",\n            name: \"Technical Layer\",\n            nameAr: \"الطبقة التقنية\",\n            emoji: \"⚙️\",\n            data: technicalLayer,\n            href: \"/technical-layer\"\n        },\n        {\n            key: \"legal-risk\",\n            name: \"Legal & Privacy\",\n            nameAr: \"القانونية والخصوصية\",\n            emoji: \"\\uD83D\\uDD12\",\n            data: legalRisk,\n            href: \"/legal-risk\"\n        }\n    ];\n    const getModuleProgress = (moduleData)=>{\n        const totalFields = Object.keys(moduleData).length;\n        const filledFields = Object.values(moduleData).filter((value)=>value && typeof value === \"string\" && value.trim()).length;\n        return totalFields > 0 ? filledFields / totalFields * 100 : 0;\n    };\n    const overallProgress = modules.reduce((total, module)=>{\n        return total + getModuleProgress(module.data);\n    }, 0) / modules.length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                        children: isArabic ? \"تقدم المشروع\" : \"Project Progress\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                        children: [\n                            Math.round(overallProgress),\n                            \"% \",\n                            isArabic ? \"مكتمل\" : \"Complete\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n                    style: {\n                        width: \"\".concat(overallProgress, \"%\")\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-5 gap-4\",\n                children: modules.map((module)=>{\n                    const progress = getModuleProgress(module.data);\n                    const isCurrent = currentModule === module.key;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: module.href,\n                        className: \"p-3 rounded-lg border-2 transition-all hover:shadow-md \".concat(isCurrent ? \"border-blue-500 bg-blue-50 dark:bg-blue-900/20\" : \"border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl mb-2\",\n                                    children: module.emoji\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs font-medium text-gray-900 dark:text-white mb-2\",\n                                    children: isArabic ? module.nameAr : module.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1 mb-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-1 rounded-full transition-all duration-300 \".concat(progress === 100 ? \"bg-green-500\" : \"bg-blue-500\"),\n                                        style: {\n                                            width: \"\".concat(progress, \"%\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                    children: [\n                                        Math.round(progress),\n                                        \"%\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 15\n                        }, this)\n                    }, module.key, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 flex justify-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/final-preview\",\n                        className: \"flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg text-sm transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-1\",\n                                children: \"\\uD83D\\uDCCB\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, this),\n                            isArabic ? \"المعاينة النهائية\" : \"Final Preview\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this),\n                    overallProgress > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{\n                            if (confirm(isArabic ? \"هل أنت متأكد من إعادة تعيين جميع البيانات؟\" : \"Are you sure you want to reset all data?\")) {\n                                // Reset functionality would go here\n                                window.location.reload();\n                            }\n                        },\n                        className: \"flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg text-sm transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-1\",\n                                children: \"\\uD83D\\uDD04\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, this),\n                            isArabic ? \"إعادة تعيين\" : \"Reset\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n_s(ProgressIndicator, \"ghodb8OExVU6fVrAbJ7D9y7GE34=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_1__.useContextStore\n    ];\n});\n_c = ProgressIndicator;\nvar _c;\n$RefreshReg$(_c, \"ProgressIndicator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ProgressIndicator.tsx\n"));

/***/ })

});