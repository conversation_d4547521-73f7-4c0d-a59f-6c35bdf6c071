'use client';

import { useContextStore } from '@/store/contextStore';

interface ProgressIndicatorProps {
  currentModule?: string;
}

export default function ProgressIndicator({ currentModule }: ProgressIndicatorProps) {
  const { 
    projectDefinition, 
    contextMap, 
    emotionalTone, 
    technicalLayer, 
    legalRisk,
    currentLanguage 
  } = useContextStore();
  
  const isArabic = currentLanguage === 'ar';

  const modules = [
    {
      key: 'project-definition',
      name: 'Project Definition',
      nameAr: 'تعريف المشروع',
      emoji: '🎯',
      data: projectDefinition,
      href: '/project-definition'
    },
    {
      key: 'context-map',
      name: 'Context Map',
      nameAr: 'خريطة السياق',
      emoji: '🗺️',
      data: contextMap,
      href: '/context-map'
    },
    {
      key: 'emotional-tone',
      name: 'Emotional Tone',
      nameAr: 'النبرة العاطفية',
      emoji: '✨',
      data: emotionalTone,
      href: '/emotional-tone'
    },
    {
      key: 'technical-layer',
      name: 'Technical Layer',
      nameAr: 'الطبقة التقنية',
      emoji: '⚙️',
      data: technicalLayer,
      href: '/technical-layer'
    },
    {
      key: 'legal-risk',
      name: 'Legal & Privacy',
      nameAr: 'القانونية والخصوصية',
      emoji: '🔒',
      data: legalRisk,
      href: '/legal-risk'
    }
  ];

  const getModuleProgress = (moduleData: any) => {
    const totalFields = Object.keys(moduleData).length;
    const filledFields = Object.values(moduleData).filter(value => 
      value && typeof value === 'string' && value.trim()
    ).length;
    return totalFields > 0 ? (filledFields / totalFields) * 100 : 0;
  };

  const overallProgress = modules.reduce((total, module) => {
    return total + getModuleProgress(module.data);
  }, 0) / modules.length;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          {isArabic ? 'تقدم المشروع' : 'Project Progress'}
        </h3>
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {Math.round(overallProgress)}% {isArabic ? 'مكتمل' : 'Complete'}
        </span>
      </div>

      {/* Overall Progress Bar */}
      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-6">
        <div 
          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
          style={{ width: `${overallProgress}%` }}
        ></div>
      </div>

      {/* Module Progress */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        {modules.map((module) => {
          const progress = getModuleProgress(module.data);
          const isCurrent = currentModule === module.key;
          
          return (
            <a
              key={module.key}
              href={module.href}
              className={`p-3 rounded-lg border-2 transition-all hover:shadow-md ${
                isCurrent 
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                  : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
              }`}
            >
              <div className="text-center">
                <div className="text-2xl mb-2">{module.emoji}</div>
                <div className="text-xs font-medium text-gray-900 dark:text-white mb-2">
                  {isArabic ? module.nameAr : module.name}
                </div>
                
                {/* Mini Progress Bar */}
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1 mb-1">
                  <div 
                    className={`h-1 rounded-full transition-all duration-300 ${
                      progress === 100 ? 'bg-green-500' : 'bg-blue-500'
                    }`}
                    style={{ width: `${progress}%` }}
                  ></div>
                </div>
                
                <div className="text-xs text-gray-600 dark:text-gray-400">
                  {Math.round(progress)}%
                </div>
              </div>
            </a>
          );
        })}
      </div>

      {/* Quick Actions */}
      <div className="mt-6 flex justify-center space-x-4">
        <a
          href="/final-preview"
          className="flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg text-sm transition-colors"
        >
          <span className="mr-1">📋</span>
          {isArabic ? 'المعاينة النهائية' : 'Final Preview'}
        </a>
        
        {overallProgress > 0 && (
          <button
            onClick={() => {
              if (confirm(isArabic ? 'هل أنت متأكد من إعادة تعيين جميع البيانات؟' : 'Are you sure you want to reset all data?')) {
                // Reset functionality would go here
                window.location.reload();
              }
            }}
            className="flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg text-sm transition-colors"
          >
            <span className="mr-1">🔄</span>
            {isArabic ? 'إعادة تعيين' : 'Reset'}
          </button>
        )}
      </div>
    </div>
  );
}
