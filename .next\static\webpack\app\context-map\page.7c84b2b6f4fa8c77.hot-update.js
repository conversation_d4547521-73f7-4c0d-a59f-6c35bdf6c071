"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/context-map/page",{

/***/ "(app-pages-browser)/./src/app/context-map/page.tsx":
/*!**************************************!*\
  !*** ./src/app/context-map/page.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ContextMap; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction ContextMap() {\n    _s();\n    const { contextMap, updateContextMap } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_1__.useContextStore)();\n    const questions = [\n        {\n            id: \"timeContext\",\n            question: \"What is the temporal context of your project?\",\n            questionAr: \"ما هو السياق الزمني لمشروعك؟\",\n            placeholder: \"e.g., Global 24/7 support, Business hours EST, Real-time responses...\",\n            placeholderAr: \"مثال: دعم عالمي على مدار الساعة، ساعات العمل بتوقيت شرق أمريكا، استجابات فورية...\",\n            aiSuggestion: \"Consider time zones, working hours, response time expectations, and any time-sensitive requirements.\",\n            aiSuggestionAr: \"فكر في المناطق الزمنية وساعات العمل وتوقعات وقت الاستجابة وأي متطلبات حساسة للوقت.\",\n            promptTemplate: 'Help me optimize this temporal context for an AI project: \"{answer}\". Suggest improvements for better time management.'\n        },\n        {\n            id: \"language\",\n            question: \"What languages should your AI system support?\",\n            questionAr: \"ما هي اللغات التي يجب أن يدعمها نظام الذكاء الاصطناعي؟\",\n            placeholder: \"e.g., English primary, Arabic secondary, Multilingual support...\",\n            placeholderAr: \"مثال: الإنجليزية أساسية، العربية ثانوية، دعم متعدد اللغات...\",\n            type: \"text\",\n            aiSuggestion: \"Consider your target audience, regional requirements, and the complexity of multilingual support.\",\n            aiSuggestionAr: \"فكر في جمهورك المستهدف والمتطلبات الإقليمية وتعقيد الدعم متعدد اللغات.\",\n            promptTemplate: 'Analyze this language requirement for an AI system: \"{answer}\". Suggest implementation strategies.'\n        },\n        {\n            id: \"location\",\n            question: \"What geographic regions or locations will this project serve?\",\n            questionAr: \"ما هي المناطق الجغرافية أو المواقع التي سيخدمها هذا المشروع؟\",\n            placeholder: \"e.g., Middle East, North America, Global, Specific cities...\",\n            placeholderAr: \"مثال: الشرق الأوسط، أمريكا الشمالية، عالمي، مدن محددة...\",\n            aiSuggestion: \"Think about regional regulations, cultural differences, and infrastructure requirements.\",\n            aiSuggestionAr: \"فكر في اللوائح الإقليمية والاختلافات الثقافية ومتطلبات البنية التحتية.\",\n            promptTemplate: 'Help me understand the geographic implications of this scope: \"{answer}\". What should I consider?'\n        }\n    ];\n    const handleInputChange = (field, value)=>{\n        setContextData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const nextStep = ()=>{\n        if (currentStep < questions.length - 1) {\n            setCurrentStep(currentStep + 1);\n        }\n    };\n    const prevStep = ()=>{\n        if (currentStep > 0) {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    const generateOutput = ()=>{\n        const markdown = \"# Context Map\\n\\n## Time Context\\n\".concat(contextData.timeContext, \"\\n\\n## Language Requirements\\n\").concat(contextData.language, \"\\n\\n## Geographic Context\\n\").concat(contextData.location, \"\\n\\n## Cultural Context\\n\").concat(contextData.culturalContext, \"\\n\\n## Behavioral Aspects\\n\").concat(contextData.behavioralAspects, \"\\n\\n## Environmental Factors\\n\").concat(contextData.environmentalFactors, \"\\n\");\n        navigator.clipboard.writeText(markdown);\n        alert(\"Context map copied to clipboard!\");\n    };\n    const currentQuestion = questions[currentStep];\n    const progress = (currentStep + 1) / questions.length * 100;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 dark:from-gray-900 dark:to-gray-800\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Header, {\n                    title: \"Context Map\",\n                    subtitle: \"Define the contextual framework for your AI project\",\n                    emoji: \"\\uD83D\\uDDFA️\",\n                    backLink: {\n                        href: \"/project-definition\",\n                        label: \"← Back to Project Definition\"\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-2xl mx-auto mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"Step \",\n                                        currentStep + 1,\n                                        \" of \",\n                                        questions.length\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        Math.round(progress),\n                                        \"% Complete\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-600 h-2 rounded-full transition-all duration-300\",\n                                style: {\n                                    width: \"\".concat(progress, \"%\")\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-2xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-semibold text-gray-900 dark:text-white mb-4\",\n                                    children: currentQuestion.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300 mb-6\",\n                                    children: currentQuestion.question\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this),\n                                currentQuestion.type === \"textarea\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: contextData[currentQuestion.id],\n                                    onChange: (e)=>handleInputChange(currentQuestion.id, e.target.value),\n                                    placeholder: currentQuestion.placeholder,\n                                    className: \"w-full p-4 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white resize-none\",\n                                    rows: 6\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: contextData[currentQuestion.id],\n                                    onChange: (e)=>handleInputChange(currentQuestion.id, e.target.value),\n                                    placeholder: currentQuestion.placeholder,\n                                    className: \"w-full p-4 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: prevStep,\n                                    disabled: currentStep === 0,\n                                    className: \"px-6 py-3 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors\",\n                                    children: \"Previous\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this),\n                                currentStep === questions.length - 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: generateOutput,\n                                            className: \"px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors\",\n                                            children: \"Copy to Clipboard\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Link, {\n                                            href: \"/vibe\",\n                                            className: \"inline-block px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors\",\n                                            children: \"Next Module →\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: nextStep,\n                                    className: \"px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors\",\n                                    children: \"Next\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 9\n                }, this),\n                Object.values(contextData).some((value)=>value.trim() !== \"\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-2xl mx-auto mt-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-gray-900 dark:text-white mb-4\",\n                            children: \"\\uD83D\\uDCCB Preview Output\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-100 dark:bg-gray-800 rounded-lg p-6 font-mono text-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"whitespace-pre-wrap text-gray-800 dark:text-gray-200\",\n                                children: \"# Context Map\\n\\n## Time Context\\n\".concat(contextData.timeContext || \"[Not specified]\", \"\\n\\n## Language Requirements\\n\").concat(contextData.language || \"[Not specified]\", \"\\n\\n## Geographic Context\\n\").concat(contextData.location || \"[Not specified]\", \"\\n\\n## Cultural Context\\n\").concat(contextData.culturalContext || \"[Not specified]\", \"\\n\\n## Behavioral Aspects\\n\").concat(contextData.behavioralAspects || \"[Not specified]\", \"\\n\\n## Environmental Factors\\n\").concat(contextData.environmentalFactors || \"[Not specified]\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n            lineNumber: 95,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, this);\n}\n_s(ContextMap, \"jlOHj64dV16jvKA34KKvBF1tiHE=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_1__.useContextStore\n    ];\n});\n_c = ContextMap;\nvar _c;\n$RefreshReg$(_c, \"ContextMap\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/context-map/page.tsx\n"));

/***/ })

});