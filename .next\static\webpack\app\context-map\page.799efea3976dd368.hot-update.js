"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/context-map/page",{

/***/ "(app-pages-browser)/./src/app/context-map/page.tsx":
/*!**************************************!*\
  !*** ./src/app/context-map/page.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ContextMap; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction ContextMap() {\n    _s();\n    const { contextMap, updateContextMap } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_1__.useContextStore)();\n    const questions = [\n        {\n            id: \"timeContext\",\n            question: \"What is the temporal context of your project?\",\n            questionAr: \"ما هو السياق الزمني لمشروعك؟\",\n            placeholder: \"e.g., Global 24/7 support, Business hours EST, Real-time responses...\",\n            placeholderAr: \"مثال: دعم عالمي على مدار الساعة، ساعات العمل بتوقيت شرق أمريكا، استجابات فورية...\",\n            aiSuggestion: \"Consider time zones, working hours, response time expectations, and any time-sensitive requirements.\",\n            aiSuggestionAr: \"فكر في المناطق الزمنية وساعات العمل وتوقعات وقت الاستجابة وأي متطلبات حساسة للوقت.\",\n            promptTemplate: 'Help me optimize this temporal context for an AI project: \"{answer}\". Suggest improvements for better time management.'\n        },\n        {\n            id: \"language\",\n            question: \"What languages should your AI system support?\",\n            questionAr: \"ما هي اللغات التي يجب أن يدعمها نظام الذكاء الاصطناعي؟\",\n            placeholder: \"e.g., English primary, Arabic secondary, Multilingual support...\",\n            placeholderAr: \"مثال: الإنجليزية أساسية، العربية ثانوية، دعم متعدد اللغات...\",\n            type: \"text\",\n            aiSuggestion: \"Consider your target audience, regional requirements, and the complexity of multilingual support.\",\n            aiSuggestionAr: \"فكر في جمهورك المستهدف والمتطلبات الإقليمية وتعقيد الدعم متعدد اللغات.\",\n            promptTemplate: 'Analyze this language requirement for an AI system: \"{answer}\". Suggest implementation strategies.'\n        },\n        {\n            id: \"location\",\n            question: \"What geographic regions or locations will this project serve?\",\n            questionAr: \"ما هي المناطق الجغرافية أو المواقع التي سيخدمها هذا المشروع؟\",\n            placeholder: \"e.g., Middle East, North America, Global, Specific cities...\",\n            placeholderAr: \"مثال: الشرق الأوسط، أمريكا الشمالية، عالمي، مدن محددة...\",\n            aiSuggestion: \"Think about regional regulations, cultural differences, and infrastructure requirements.\",\n            aiSuggestionAr: \"فكر في اللوائح الإقليمية والاختلافات الثقافية ومتطلبات البنية التحتية.\",\n            promptTemplate: 'Help me understand the geographic implications of this scope: \"{answer}\". What should I consider?'\n        }\n    ];\n    const handleFieldChange = (field, value)=>{\n        updateContextMap({\n            [field]: value\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 dark:from-gray-900 dark:to-gray-800\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Header, {\n                    title: \"Context Map\",\n                    subtitle: \"Define the contextual framework for your AI project\",\n                    emoji: \"\\uD83D\\uDDFA️\",\n                    backLink: {\n                        href: \"/project-definition\",\n                        label: \"← Back to Project Definition\"\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-2xl mx-auto mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"Step \",\n                                        currentStep + 1,\n                                        \" of \",\n                                        questions.length\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        Math.round(progress),\n                                        \"% Complete\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-600 h-2 rounded-full transition-all duration-300\",\n                                style: {\n                                    width: \"\".concat(progress, \"%\")\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-2xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-semibold text-gray-900 dark:text-white mb-4\",\n                                    children: currentQuestion.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300 mb-6\",\n                                    children: currentQuestion.question\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this),\n                                currentQuestion.type === \"textarea\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: contextData[currentQuestion.id],\n                                    onChange: (e)=>handleInputChange(currentQuestion.id, e.target.value),\n                                    placeholder: currentQuestion.placeholder,\n                                    className: \"w-full p-4 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white resize-none\",\n                                    rows: 6\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: contextData[currentQuestion.id],\n                                    onChange: (e)=>handleInputChange(currentQuestion.id, e.target.value),\n                                    placeholder: currentQuestion.placeholder,\n                                    className: \"w-full p-4 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: prevStep,\n                                    disabled: currentStep === 0,\n                                    className: \"px-6 py-3 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors\",\n                                    children: \"Previous\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this),\n                                currentStep === questions.length - 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: generateOutput,\n                                            className: \"px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors\",\n                                            children: \"Copy to Clipboard\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Link, {\n                                            href: \"/vibe\",\n                                            className: \"inline-block px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors\",\n                                            children: \"Next Module →\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: nextStep,\n                                    className: \"px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors\",\n                                    children: \"Next\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, this),\n                Object.values(contextData).some((value)=>value.trim() !== \"\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-2xl mx-auto mt-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-gray-900 dark:text-white mb-4\",\n                            children: \"\\uD83D\\uDCCB Preview Output\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-100 dark:bg-gray-800 rounded-lg p-6 font-mono text-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"whitespace-pre-wrap text-gray-800 dark:text-gray-200\",\n                                children: \"# Context Map\\n\\n## Time Context\\n\".concat(contextData.timeContext || \"[Not specified]\", \"\\n\\n## Language Requirements\\n\").concat(contextData.language || \"[Not specified]\", \"\\n\\n## Geographic Context\\n\").concat(contextData.location || \"[Not specified]\", \"\\n\\n## Cultural Context\\n\").concat(contextData.culturalContext || \"[Not specified]\", \"\\n\\n## Behavioral Aspects\\n\").concat(contextData.behavioralAspects || \"[Not specified]\", \"\\n\\n## Environmental Factors\\n\").concat(contextData.environmentalFactors || \"[Not specified]\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n_s(ContextMap, \"jlOHj64dV16jvKA34KKvBF1tiHE=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_1__.useContextStore\n    ];\n});\n_c = ContextMap;\nvar _c;\n$RefreshReg$(_c, \"ContextMap\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/context-map/page.tsx\n"));

/***/ })

});