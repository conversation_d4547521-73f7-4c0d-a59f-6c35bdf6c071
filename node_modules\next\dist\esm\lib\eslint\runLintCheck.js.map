{"version": 3, "sources": ["../../../src/lib/eslint/runLintCheck.ts"], "names": ["promises", "fs", "existsSync", "bold", "cyan", "red", "underline", "yellow", "path", "findUp", "semver", "CommentJson", "formatResults", "writeDefaultConfig", "hasEslintConfiguration", "writeOutputFile", "ESLINT_PROMPT_VALUES", "findPagesDir", "installDependencies", "hasNecessaryDependencies", "Log", "isError", "getProperError", "getPkgManager", "VALID_SEVERITY", "isValidSeverity", "severity", "includes", "requiredPackages", "file", "pkg", "exportsRestrict", "cliPrompt", "console", "log", "cliSelect", "Promise", "resolve", "require", "default", "value", "values", "valueR<PERSON><PERSON>", "title", "recommended", "selected", "name", "unselected", "config", "lint", "baseDir", "lintDirs", "eslintrcFile", "pkgJsonPath", "lintDuringBuild", "eslintOptions", "reportErrorsOnly", "maxWarnings", "formatter", "outputFile", "mod", "ESLint", "deps", "packageManager", "missing", "some", "dep", "error", "resolved", "get", "eslintVersion", "version", "CLIEngine", "lt", "options", "useEslintrc", "baseConfig", "errorOnUnmatchedPattern", "extensions", "cache", "eslint", "nextEslintPluginIsEnabled", "nextRulesEnabled", "Map", "configFile", "completeConfig", "calculateConfigForFile", "plugins", "Object", "entries", "rules", "startsWith", "length", "set", "pagesDir", "pagesDirRules", "updatedPagesDir", "rule", "replace", "warn", "lintStart", "process", "hrtime", "results", "lintFiles", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fix", "outputFixes", "getErrorResults", "loadFormatter", "formattedResult", "format", "lintEnd", "totalWarnings", "reduce", "sum", "warningCount", "output", "outputWithMessages", "eventInfo", "durationInSeconds", "lintedFilesCount", "lintFix", "nextEslintPluginVersion", "has", "join", "dirname", "nextEslintPluginErrorsCount", "totalNextPluginErrorCount", "nextEslintPluginWarningsCount", "totalNextPluginWarningCount", "fromEntries", "err", "message", "runLintCheck", "opts", "strict", "cwd", "packageJsonConfig", "pkgJsonContent", "readFile", "encoding", "parse", "exists", "emptyPkgJsonConfig", "emptyEslintrc", "selectedConfig", "find", "opt", "for<PERSON>ach", "dir", "ready"], "mappings": "AAAA,SAASA,YAAYC,EAAE,EAAEC,UAAU,QAAQ,KAAI;AAC/C,SAASC,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,SAAS,EAAEC,MAAM,QAAQ,gBAAe;AAClE,OAAOC,UAAU,OAAM;AAEvB,OAAOC,YAAY,6BAA4B;AAC/C,OAAOC,YAAY,4BAA2B;AAC9C,YAAYC,iBAAiB,kCAAiC;AAE9D,SAASC,aAAa,QAAQ,oBAAmB;AAEjD,SAASC,kBAAkB,QAAQ,uBAAsB;AACzD,SAASC,sBAAsB,QAAQ,2BAA0B;AACjE,SAASC,eAAe,QAAQ,oBAAmB;AAEnD,SAASC,oBAAoB,QAAQ,eAAc;AACnD,SAASC,YAAY,QAAQ,oBAAmB;AAChD,SAASC,mBAAmB,QAAQ,0BAAyB;AAC7D,SAASC,wBAAwB,QAAQ,gCAA+B;AAExE,YAAYC,SAAS,yBAAwB;AAE7C,OAAOC,WAAWC,cAAc,QAAQ,cAAa;AACrD,SAASC,aAAa,QAAQ,6BAA4B;AAO1D,8GAA8G;AAC9G,MAAMC,iBAAiB;IAAC;IAAO;IAAQ;CAAQ;AAG/C,SAASC,gBAAgBC,QAAgB;IACvC,OAAOF,eAAeG,QAAQ,CAACD;AACjC;AAEA,MAAME,mBAAmB;IACvB;QAAEC,MAAM;QAAUC,KAAK;QAAUC,iBAAiB;IAAM;IACxD;QACEF,MAAM;QACNC,KAAK;QACLC,iBAAiB;IACnB;CACD;AAED,eAAeC;IACbC,QAAQC,GAAG,CACT/B,KACE,CAAC,EAAEC,KACD,KACA,sFAAsF,CAAC;IAI7F,IAAI;QACF,MAAM+B,YAAY,AAChB,CAAA,MAAMC,QAAQC,OAAO,CAACC,QAAQ,iCAAgC,EAC9DC,OAAO;QACT,MAAM,EAAEC,KAAK,EAAE,GAAG,MAAML,UAAU;YAChCM,QAAQzB;YACR0B,eAAe,CACb,EACEC,KAAK,EACLC,WAAW,EAC2C,EACxDC;gBAEA,MAAMC,OAAOD,WAAW1C,KAAKG,UAAUF,KAAKuC,WAAWA;gBACvD,OAAOG,OAAQF,CAAAA,cAAczC,KAAKI,OAAO,qBAAqB,EAAC;YACjE;YACAsC,UAAUzC,KAAK;YACf2C,YAAY;QACd;QAEA,OAAO;YAAEC,QAAQR,CAAAA,yBAAAA,MAAOQ,MAAM,KAAI;QAAK;IACzC,EAAE,OAAM;QACN,OAAO;YAAEA,QAAQ;QAAK;IACxB;AACF;AAEA,eAAeC,KACbC,OAAe,EACfC,QAAkB,EAClBC,YAA2B,EAC3BC,WAA0B,EAC1B,EACEC,kBAAkB,KAAK,EACvBC,gBAAgB,IAAI,EACpBC,mBAAmB,KAAK,EACxBC,cAAc,CAAC,CAAC,EAChBC,YAAY,IAAI,EAChBC,aAAa,IAAI,EAQlB;IAUD,IAAI;YAyBqCC,gBA8GnCC;QAtIJ,0CAA0C;QAC1C,MAAMC,OAAO,MAAM3C,yBAAyB+B,SAAStB;QACrD,MAAMmC,iBAAiBxC,cAAc2B;QAErC,IAAIY,KAAKE,OAAO,CAACC,IAAI,CAAC,CAACC,MAAQA,IAAIpC,GAAG,KAAK,WAAW;YACpDV,IAAI+C,KAAK,CACP,CAAC,wBAAwB,EACvBb,kBAAkB,oCAAoC,IACvD,CAAC,EAAEnD,KACFC,KACE,AAAC2D,CAAAA,mBAAmB,SAChB,mBACAA,mBAAmB,SACnB,4BACA,wBAAuB,IAAK,YAElC,CAAC;YAEL,OAAO;QACT;QAEA,MAAMH,MAAM,MAAMxB,QAAQC,OAAO,CAACC,QAAQwB,KAAKM,QAAQ,CAACC,GAAG,CAAC;QAE5D,MAAM,EAAER,MAAM,EAAE,GAAGD;QACnB,IAAIU,gBAAgBT,CAAAA,0BAAAA,OAAQU,OAAO,OAAIX,iBAAAA,IAAIY,SAAS,qBAAbZ,eAAeW,OAAO;QAE7D,IAAI,CAACD,iBAAiB5D,OAAO+D,EAAE,CAACH,eAAe,UAAU;YACvD,OAAO,CAAC,EAAEjE,IACR,SACA,wDAAwD,EACxDiE,gBAAgB,OAAOA,gBAAgB,MAAM,GAC9C,6CAA6C,CAAC;QACjD;QAEA,IAAII,UAAe;YACjBC,aAAa;YACbC,YAAY,CAAC;YACbC,yBAAyB;YACzBC,YAAY;gBAAC;gBAAO;gBAAQ;gBAAO;aAAO;YAC1CC,OAAO;YACP,GAAGxB,aAAa;QAClB;QAEA,IAAIyB,SAAS,IAAInB,OAAOa;QAExB,IAAIO,4BAA4B;QAChC,MAAMC,mBAAmB,IAAIC;QAE7B,KAAK,MAAMC,cAAc;YAAChC;YAAcC;SAAY,CAAE;gBAOhDgC;YANJ,IAAI,CAACD,YAAY;YAEjB,MAAMC,iBAAyB,MAAML,OAAOM,sBAAsB,CAChEF;YAGF,KAAIC,0BAAAA,eAAeE,OAAO,qBAAtBF,wBAAwB1D,QAAQ,CAAC,eAAe;gBAClDsD,4BAA4B;gBAC5B,KAAK,MAAM,CAACnC,MAAM,CAACpB,SAAS,CAAC,IAAI8D,OAAOC,OAAO,CAACJ,eAAeK,KAAK,EAAG;oBACrE,IAAI,CAAC5C,KAAK6C,UAAU,CAAC,gBAAgB;wBACnC;oBACF;oBACA,IACE,OAAOjE,aAAa,YACpBA,YAAY,KACZA,WAAWF,eAAeoE,MAAM,EAChC;wBACAV,iBAAiBW,GAAG,CAAC/C,MAAMtB,cAAc,CAACE,SAAS;oBACrD,OAAO,IACL,OAAOA,aAAa,YACpBD,gBAAgBC,WAChB;wBACAwD,iBAAiBW,GAAG,CAAC/C,MAAMpB;oBAC7B;gBACF;gBACA;YACF;QACF;QAEA,MAAMoE,WAAW7E,aAAaiC,SAAS4C,QAAQ;QAC/C,MAAMC,gBAAgBD,WAAW;YAAC;SAAoC,GAAG,EAAE;QAE3E,IAAIb,2BAA2B;YAC7B,IAAIe,kBAAkB;YAEtB,KAAK,MAAMC,QAAQF,cAAe;oBAE7BrB,2BACAA;gBAFH,IACE,GAACA,4BAAAA,QAAQE,UAAU,CAAEc,KAAK,qBAAzBhB,yBAA2B,CAACuB,KAAK,KAClC,GAACvB,6BAAAA,QAAQE,UAAU,CAAEc,KAAK,qBAAzBhB,0BAA2B,CAC1BuB,KAAKC,OAAO,CAAC,cAAc,2BAC5B,GACD;oBACA,IAAI,CAACxB,QAAQE,UAAU,CAAEc,KAAK,EAAE;wBAC9BhB,QAAQE,UAAU,CAAEc,KAAK,GAAG,CAAC;oBAC/B;oBACAhB,QAAQE,UAAU,CAAEc,KAAK,CAACO,KAAK,GAAG;wBAAC;wBAAGH;qBAAS;oBAC/CE,kBAAkB;gBACpB;YACF;YAEA,IAAIA,iBAAiB;gBACnBhB,SAAS,IAAInB,OAAOa;YACtB;QACF,OAAO;YACLtD,IAAI+E,IAAI,CAAC;YACT/E,IAAI+E,IAAI,CACN;QAEJ;QAEA,MAAMC,YAAYC,QAAQC,MAAM;QAEhC,IAAIC,UAAU,MAAMvB,OAAOwB,SAAS,CAACrD;QACrC,IAAIsD,oBAAoB;QAExB,IAAI/B,QAAQgC,GAAG,EAAE,MAAM7C,OAAO8C,WAAW,CAACJ;QAC1C,IAAI/C,kBAAkB+C,UAAU,MAAM1C,OAAO+C,eAAe,CAACL,SAAS,6CAA6C;;QAEnH,IAAI7C,WAAW+C,oBAAoB,MAAMzB,OAAO6B,aAAa,CAACnD;QAC9D,MAAMoD,kBAAkBlG,cACtBsC,SACAqD,SACAE,qCAAAA,kBAAmBM,MAAM;QAE3B,MAAMC,UAAUX,QAAQC,MAAM,CAACF;QAC/B,MAAMa,gBAAgBV,QAAQW,MAAM,CAClC,CAACC,KAAatF,OAAqBsF,MAAMtF,KAAKuF,YAAY,EAC1D;QAGF,IAAIzD,YAAY,MAAM5C,gBAAgB4C,YAAYmD,gBAAgBO,MAAM;QAExE,OAAO;YACLA,QAAQP,gBAAgBQ,kBAAkB;YAC1CjG,SACEwC,EAAAA,0BAAAA,OAAO+C,eAAe,CAACL,6BAAvB1C,wBAAiC+B,MAAM,IAAG,KACzCnC,eAAe,KAAKwD,gBAAgBxD;YACvC8D,WAAW;gBACTC,mBAAmBR,OAAO,CAAC,EAAE;gBAC7B1C,eAAeA;gBACfmD,kBAAkBlB,QAAQX,MAAM;gBAChC8B,SAAS,CAAC,CAAChD,QAAQgC,GAAG;gBACtBiB,yBACE1C,6BAA6BnB,KAAKM,QAAQ,CAACwD,GAAG,CAAC,wBAC3CtF,QAAQ9B,KAAKqH,IAAI,CACfrH,KAAKsH,OAAO,CAAChE,KAAKM,QAAQ,CAACC,GAAG,CAAC,wBAC/B,iBACCE,OAAO,GACV;gBACNwD,6BAA6BjB,gBAAgBkB,yBAAyB;gBACtEC,+BACEnB,gBAAgBoB,2BAA2B;gBAC7ChD,kBAAkBM,OAAO2C,WAAW,CAACjD;YACvC;QACF;IACF,EAAE,OAAOkD,KAAK;QACZ,IAAI9E,iBAAiB;YACnBlC,IAAI+C,KAAK,CACP,CAAC,QAAQ,EACP9C,QAAQ+G,QAAQA,IAAIC,OAAO,GAAGD,IAAIC,OAAO,CAACnC,OAAO,CAAC,OAAO,OAAOkC,IACjE,CAAC;YAEJ,OAAO;QACT,OAAO;YACL,MAAM9G,eAAe8G;QACvB;IACF;AACF;AAEA,OAAO,eAAeE,aACpBpF,OAAe,EACfC,QAAkB,EAClBoF,IAQC;IAED,MAAM,EACJjF,kBAAkB,KAAK,EACvBC,gBAAgB,IAAI,EACpBC,mBAAmB,KAAK,EACxBC,cAAc,CAAC,CAAC,EAChBC,YAAY,IAAI,EAChBC,aAAa,IAAI,EACjB6E,SAAS,KAAK,EACf,GAAGD;IACJ,IAAI;QACF,6BAA6B;QAC7B,qGAAqG;QACrG,MAAMnF,eACJ,AAAC,MAAM3C,OACL;YACE;YACA;YACA;YACA;YACA;YACA;SACD,EACD;YACEgI,KAAKvF;QACP,MACI;QAER,MAAMG,cAAc,AAAC,MAAM5C,OAAO,gBAAgB;YAAEgI,KAAKvF;QAAQ,MAAO;QACxE,IAAIwF,oBAAoB;QACxB,IAAIrF,aAAa;YACf,MAAMsF,iBAAiB,MAAM1I,GAAG2I,QAAQ,CAACvF,aAAa;gBACpDwF,UAAU;YACZ;YACAH,oBAAoB/H,YAAYmI,KAAK,CAACH;QACxC;QAEA,MAAM3F,SAAS,MAAMlC,uBAAuBsC,cAAcsF;QAC1D,IAAI5E;QAEJ,IAAId,OAAO+F,MAAM,EAAE;YACjB,8BAA8B;YAC9B,OAAO,MAAM9F,KAAKC,SAASC,UAAUC,cAAcC,aAAa;gBAC9DC;gBACAC;gBACAC;gBACAC;gBACAC;gBACAC;YACF;QACF,OAAO;YACL,+DAA+D;YAC/D,4DAA4D;YAC5D,8BAA8B;YAC9B,IAAIL,iBAAiB;gBACnB,IAAIN,OAAOgG,kBAAkB,IAAIhG,OAAOiG,aAAa,EAAE;oBACrD7H,IAAI+E,IAAI,CACN,CAAC,sCAAsC,EAAEhG,KACvCC,KAAK,cACL,eAAe,CAAC;gBAEtB;gBACA,OAAO;YACT,OAAO;gBACL,sFAAsF;gBACtF,MAAM,EAAE4C,QAAQkG,cAAc,EAAE,GAAGV,SAC/BxH,qBAAqBmI,IAAI,CACvB,CAACC,MAA2BA,IAAIzG,KAAK,KAAK,YAE5C,MAAMX;gBAEV,IAAIkH,kBAAkB,MAAM;oBAC1B,oDAAoD;oBACpD9H,IAAI+E,IAAI,CACN;oBAEF,OAAO;gBACT,OAAO;oBACL,sEAAsE;oBACtErC,OAAO,MAAM3C,yBAAyB+B,SAAStB;oBAC/C,IAAIkC,KAAKE,OAAO,CAAC4B,MAAM,GAAG,GAAG;wBAC3B9B,KAAKE,OAAO,CAACqF,OAAO,CAAC,CAACnF;4BACpB,IAAIA,IAAIpC,GAAG,KAAK,UAAU;gCACxB,0FAA0F;gCAC1FoC,IAAIpC,GAAG,GAAG;4BACZ;wBACF;wBAEA,MAAMZ,oBAAoBgC,SAASY,KAAKE,OAAO,EAAE;oBACnD;oBAEA,+BAA+B;oBAC/B,gFAAgF;oBAChF,IACE;wBAAC;wBAAO;wBAAW;wBAAS;qBAAY,CAACC,IAAI,CAAC,CAACqF,MAC7CpJ,WAAWM,KAAKqH,IAAI,CAAC3E,SAASoG,QAEhC;wBACA,MAAMzI,mBACJqC,SACAF,QACAkG,gBACA9F,cACAC,aACAqF;oBAEJ;gBACF;gBAEAtH,IAAImI,KAAK,CACP,CAAC,6CAA6C,EAAEpJ,KAC9CC,KAAK,cACL,mCAAmC,CAAC;gBAGxC,OAAO;YACT;QACF;IACF,EAAE,OAAOgI,KAAK;QACZ,MAAMA;IACR;AACF"}