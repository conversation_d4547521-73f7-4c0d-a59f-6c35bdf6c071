"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/project-definition/page",{

/***/ "(app-pages-browser)/./src/components/ModuleLayout.tsx":
/*!*****************************************!*\
  !*** ./src/components/ModuleLayout.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ModuleLayout; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Header */ \"(app-pages-browser)/./src/components/Header.tsx\");\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction ModuleLayout(param) {\n    let { title, titleAr, subtitle, subtitleAr, emoji, backLink, nextLink, children, rightPanel } = param;\n    _s();\n    const { currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    title: isArabic ? titleAr : title,\n                    subtitle: isArabic ? subtitleAr : subtitle,\n                    emoji: emoji,\n                    backLink: backLink ? {\n                        href: backLink.href,\n                        label: isArabic ? backLink.labelAr : backLink.label\n                    } : undefined\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-2 gap-8 max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6 \".concat(isArabic ? \"lg:order-2\" : \"lg:order-1\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl mr-3\",\n                                                children: \"✍️\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                lineNumber: 63,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                                                children: isArabic ? \"الأسئلة الذكية\" : \"Smart Questions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 15\n                                    }, this),\n                                    children\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6 \".concat(isArabic ? \"lg:order-1\" : \"lg:order-2\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 sticky top-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl mr-3\",\n                                                    children: \"\\uD83D\\uDCC4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                    lineNumber: 77,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                                                    children: isArabic ? \"المخرجات المجمّعة\" : \"Generated Outputs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                    lineNumber: 78,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, this),\n                                    rightPanel\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this),\n                (backLink || nextLink) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mt-12 max-w-7xl mx-auto\",\n                    children: [\n                        backLink ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: backLink.href,\n                            className: \"flex items-center px-6 py-3 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"mr-2\",\n                                    children: \"←\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 17\n                                }, this),\n                                isArabic ? backLink.labelAr : backLink.label\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 15\n                        }, this),\n                        nextLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: nextLink.href,\n                            className: \"flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors\",\n                            children: [\n                                isArabic ? nextLink.labelAr : nextLink.label,\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2\",\n                                    children: \"→\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n            lineNumber: 45,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n_s(ModuleLayout, \"TUcpdwZ+GxByrtxz7lNUh3/XmDk=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore\n    ];\n});\n_c = ModuleLayout;\nvar _c;\n$RefreshReg$(_c, \"ModuleLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ModuleLayout.tsx\n"));

/***/ })

});