export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-16">
        {/* Header */}
        <header className="text-center mb-16">
          <h1 className="text-5xl font-bold text-gray-900 dark:text-white mb-4">
            🧠 ContextKit
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            An innovative, multi-page interactive web application designed to streamline 
            the creation of organized, coherent, and actionable context for AI-driven projects.
          </p>
        </header>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
            <div className="text-3xl mb-4">🧭</div>
            <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">
              Multi-page Interface
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              Dedicated pages for each module with tailored prompts and outputs.
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
            <div className="text-3xl mb-4">✍️</div>
            <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">
              Guided Prompts
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              Smart questions to guide your thought process through each module.
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
            <div className="text-3xl mb-4">📋</div>
            <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">
              Output Copying
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              Easy-to-use buttons for copying entire outputs or individual responses.
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
            <div className="text-3xl mb-4">🧾</div>
            <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">
              Structured Outputs
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              Outputs in Markdown, HTML, or JSON for easy integration.
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
            <div className="text-3xl mb-4">💾</div>
            <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">
              Auto-save & Sessions
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              Automatic saving and retrieval of user sessions for convenience.
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
            <div className="text-3xl mb-4">🌐</div>
            <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">
              Multilingual Support
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              English and Arabic interfaces to cater to a wider audience.
            </p>
          </div>
        </div>

        {/* Modules Section */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-12 text-gray-900 dark:text-white">
            🛠️ Available Modules
          </h2>
          <div className="grid md:grid-cols-2 gap-6">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-blue-500">
              <h3 className="text-xl font-semibold mb-3 text-gray-900 dark:text-white">
                Project Definition
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Outline the scope, users, and goals of your project with guided prompts.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-green-500">
              <h3 className="text-xl font-semibold mb-3 text-gray-900 dark:text-white">
                Context Map
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Define time, language, location, and behavioral aspects of your project.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-purple-500">
              <h3 className="text-xl font-semibold mb-3 text-gray-900 dark:text-white">
                Vibe & Experience
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Capture the overall tone and user experience desired for your project.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-orange-500">
              <h3 className="text-xl font-semibold mb-3 text-gray-900 dark:text-white">
                Technical Layer
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Define technical requirements, tools, and models to be used.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-red-500">
              <h3 className="text-xl font-semibold mb-3 text-gray-900 dark:text-white">
                Challenges & Privacy
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Address challenges, privacy concerns, and regulatory issues.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-indigo-500">
              <h3 className="text-xl font-semibold mb-3 text-gray-900 dark:text-white">
                Context Management
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Options for compressing, persisting, and isolating context.
              </p>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <div className="text-center">
          <a
            href="/project-definition"
            className="inline-block bg-blue-600 hover:bg-blue-700 text-white font-bold py-4 px-8 rounded-lg text-lg transition-colors duration-200"
          >
            Start Building Context
          </a>
          <p className="mt-4 text-gray-600 dark:text-gray-300">
            Ready to create structured, actionable context for your AI projects?
          </p>
        </div>
      </div>
    </div>
  );
}
