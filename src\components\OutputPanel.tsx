'use client';

import { useState } from 'react';
import { useContextStore } from '@/store/contextStore';

interface OutputPanelProps {
  moduleData: any;
  moduleName: string;
  moduleNameAr: string;
}

export default function OutputPanel({ moduleData, moduleName, moduleNameAr }: OutputPanelProps) {
  const { currentLanguage, outputFormat, setOutputFormat } = useContextStore();
  const [copied, setCopied] = useState(false);
  const isArabic = currentLanguage === 'ar';

  // تحويل البيانات إلى تنسيقات مختلفة
  const generateMarkdown = () => {
    const title = isArabic ? moduleNameAr : moduleName;
    let markdown = `# ${title}\n\n`;
    
    Object.entries(moduleData).forEach(([key, value]) => {
      if (value && typeof value === 'string' && value.trim()) {
        const formattedKey = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
        markdown += `## ${formattedKey}\n${value}\n\n`;
      }
    });
    
    return markdown;
  };

  const generateHTML = () => {
    const title = isArabic ? moduleNameAr : moduleName;
    let html = `<div class="module-output">\n  <h1>${title}</h1>\n`;
    
    Object.entries(moduleData).forEach(([key, value]) => {
      if (value && typeof value === 'string' && value.trim()) {
        const formattedKey = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
        html += `  <section>\n    <h2>${formattedKey}</h2>\n    <p>${value}</p>\n  </section>\n`;
      }
    });
    
    html += '</div>';
    return html;
  };

  const generateJSON = () => {
    const filteredData = Object.fromEntries(
      Object.entries(moduleData).filter(([_, value]) => 
        value && typeof value === 'string' && value.trim()
      )
    );
    
    return JSON.stringify({
      module: isArabic ? moduleNameAr : moduleName,
      data: filteredData,
      timestamp: new Date().toISOString()
    }, null, 2);
  };

  const getCurrentOutput = () => {
    switch (outputFormat) {
      case 'markdown': return generateMarkdown();
      case 'html': return generateHTML();
      case 'json': return generateJSON();
      default: return generateMarkdown();
    }
  };

  const handleCopyAll = async () => {
    const output = getCurrentOutput();
    await navigator.clipboard.writeText(output);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const handleDownload = () => {
    const output = getCurrentOutput();
    const extension = outputFormat === 'json' ? 'json' : outputFormat === 'html' ? 'html' : 'md';
    const filename = `${moduleName.toLowerCase().replace(/\s+/g, '-')}.${extension}`;
    
    const blob = new Blob([output], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const hasData = Object.values(moduleData).some(value => 
    value && typeof value === 'string' && value.trim()
  );

  return (
    <div className="space-y-4">
      {/* Format Selector */}
      <div className="flex items-center justify-between">
        <div className="flex space-x-2">
          {(['markdown', 'html', 'json'] as const).map((format) => (
            <button
              key={format}
              onClick={() => setOutputFormat(format)}
              className={`px-3 py-1 text-sm rounded-lg transition-colors ${
                outputFormat === format
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
              }`}
            >
              {format.toUpperCase()}
            </button>
          ))}
        </div>

        {/* Action Buttons */}
        {hasData && (
          <div className="flex space-x-2">
            <button
              onClick={handleCopyAll}
              className="flex items-center px-3 py-1 text-sm bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded-lg hover:bg-green-200 dark:hover:bg-green-900/30 transition-colors"
            >
              <span className="mr-1">📋</span>
              {copied ? (isArabic ? 'تم النسخ!' : 'Copied!') : (isArabic ? 'نسخ الكل' : 'Copy All')}
            </button>
            
            <button
              onClick={handleDownload}
              className="flex items-center px-3 py-1 text-sm bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-900/30 transition-colors"
            >
              <span className="mr-1">💾</span>
              {isArabic ? 'تحميل' : 'Download'}
            </button>
          </div>
        )}
      </div>

      {/* Output Display */}
      <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4 min-h-[300px]">
        {hasData ? (
          <pre className={`text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap font-mono overflow-x-auto ${
            isArabic ? 'text-right' : 'text-left'
          }`}>
            {getCurrentOutput()}
          </pre>
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500 dark:text-gray-400">
            <div className="text-center">
              <span className="text-4xl mb-2 block">📝</span>
              <p>{isArabic ? 'ابدأ بالإجابة على الأسئلة لرؤية المخرجات' : 'Start answering questions to see outputs'}</p>
            </div>
          </div>
        )}
      </div>

      {/* Format Info */}
      <div className="text-xs text-gray-500 dark:text-gray-400">
        {outputFormat === 'markdown' && (isArabic ? 'تنسيق Markdown - جاهز للاستخدام في المستندات' : 'Markdown format - Ready for documentation')}
        {outputFormat === 'html' && (isArabic ? 'تنسيق HTML - جاهز للمواقع الإلكترونية' : 'HTML format - Ready for websites')}
        {outputFormat === 'json' && (isArabic ? 'تنسيق JSON - جاهز للبرمجة والـ APIs' : 'JSON format - Ready for programming and APIs')}
      </div>
    </div>
  );
}
