'use client';

import { useState } from 'react';
import Link from 'next/link';

interface ContextData {
  timeContext: string;
  language: string;
  location: string;
  culturalContext: string;
  behavioralAspects: string;
  environmentalFactors: string;
}

export default function ContextMap() {
  const [contextData, setContextData] = useState<ContextData>({
    timeContext: '',
    language: '',
    location: '',
    culturalContext: '',
    behavioralAspects: '',
    environmentalFactors: ''
  });

  const [currentStep, setCurrentStep] = useState(0);

  const questions = [
    {
      id: 'timeContext',
      title: 'Time Context',
      question: 'What is the temporal context of your project? (Time zones, working hours, deadlines)',
      placeholder: 'e.g., Global 24/7 support, Business hours EST, Real-time responses...',
      type: 'textarea'
    },
    {
      id: 'language',
      title: 'Language Requirements',
      question: 'What languages should your AI system support?',
      placeholder: 'e.g., English primary, Spanish secondary, Multilingual support...',
      type: 'text'
    },
    {
      id: 'location',
      title: 'Geographic Context',
      question: 'What geographic regions or locations will this project serve?',
      placeholder: 'e.g., North America, Global, Specific cities or countries...',
      type: 'textarea'
    },
    {
      id: 'culturalContext',
      title: 'Cultural Context',
      question: 'What cultural considerations should be taken into account?',
      placeholder: 'e.g., Cultural sensitivity, Local customs, Communication styles...',
      type: 'textarea'
    },
    {
      id: 'behavioralAspects',
      title: 'Behavioral Aspects',
      question: 'What user behaviors and interaction patterns should the system understand?',
      placeholder: 'e.g., User expertise levels, Preferred communication styles, Usage patterns...',
      type: 'textarea'
    },
    {
      id: 'environmentalFactors',
      title: 'Environmental Factors',
      question: 'What environmental or situational factors affect the project context?',
      placeholder: 'e.g., Mobile vs desktop usage, Noisy environments, Accessibility needs...',
      type: 'textarea'
    }
  ];

  const handleInputChange = (field: keyof ContextData, value: string) => {
    setContextData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const nextStep = () => {
    if (currentStep < questions.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const generateOutput = () => {
    const markdown = `# Context Map

## Time Context
${contextData.timeContext}

## Language Requirements
${contextData.language}

## Geographic Context
${contextData.location}

## Cultural Context
${contextData.culturalContext}

## Behavioral Aspects
${contextData.behavioralAspects}

## Environmental Factors
${contextData.environmentalFactors}
`;

    navigator.clipboard.writeText(markdown);
    alert('Context map copied to clipboard!');
  };

  const currentQuestion = questions[currentStep];
  const progress = ((currentStep + 1) / questions.length) * 100;

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <header className="text-center mb-8">
          <Link href="/project-definition" className="text-green-600 hover:text-green-800 mb-4 inline-block">
            ← Back to Project Definition
          </Link>
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-2">
            🗺️ Context Map
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            Define the contextual framework for your AI project
          </p>
        </header>

        {/* Progress Bar */}
        <div className="max-w-2xl mx-auto mb-8">
          <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
            <span>Step {currentStep + 1} of {questions.length}</span>
            <span>{Math.round(progress)}% Complete</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              className="bg-green-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        </div>

        {/* Question Card */}
        <div className="max-w-2xl mx-auto">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
              {currentQuestion.title}
            </h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              {currentQuestion.question}
            </p>
            
            {currentQuestion.type === 'textarea' ? (
              <textarea
                value={contextData[currentQuestion.id as keyof ContextData]}
                onChange={(e) => handleInputChange(currentQuestion.id as keyof ContextData, e.target.value)}
                placeholder={currentQuestion.placeholder}
                className="w-full p-4 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white resize-none"
                rows={6}
              />
            ) : (
              <input
                type="text"
                value={contextData[currentQuestion.id as keyof ContextData]}
                onChange={(e) => handleInputChange(currentQuestion.id as keyof ContextData, e.target.value)}
                placeholder={currentQuestion.placeholder}
                className="w-full p-4 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
            )}
          </div>

          {/* Navigation Buttons */}
          <div className="flex justify-between">
            <button
              onClick={prevStep}
              disabled={currentStep === 0}
              className="px-6 py-3 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors"
            >
              Previous
            </button>
            
            {currentStep === questions.length - 1 ? (
              <div className="space-x-4">
                <button
                  onClick={generateOutput}
                  className="px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
                >
                  Copy to Clipboard
                </button>
                <Link
                  href="/vibe"
                  className="inline-block px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
                >
                  Next Module →
                </Link>
              </div>
            ) : (
              <button
                onClick={nextStep}
                className="px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
              >
                Next
              </button>
            )}
          </div>
        </div>

        {/* Output Preview */}
        {Object.values(contextData).some(value => value.trim() !== '') && (
          <div className="max-w-2xl mx-auto mt-12">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              📋 Preview Output
            </h3>
            <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-6 font-mono text-sm">
              <pre className="whitespace-pre-wrap text-gray-800 dark:text-gray-200">
{`# Context Map

## Time Context
${contextData.timeContext || '[Not specified]'}

## Language Requirements
${contextData.language || '[Not specified]'}

## Geographic Context
${contextData.location || '[Not specified]'}

## Cultural Context
${contextData.culturalContext || '[Not specified]'}

## Behavioral Aspects
${contextData.behavioralAspects || '[Not specified]'}

## Environmental Factors
${contextData.environmentalFactors || '[Not specified]'}`}
              </pre>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
