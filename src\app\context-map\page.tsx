'use client';

import ModuleLayout from '@/components/ModuleLayout';
import SmartQuestion from '@/components/SmartQuestion';
import OutputPanel from '@/components/OutputPanel';
import { useContextStore } from '@/store/contextStore';

export default function ContextMap() {
  const { contextMap, updateContextMap } = useContextStore();

  const questions = [
    {
      id: 'timeContext',
      question: 'What is the temporal context of your project?',
      questionAr: 'ما هو السياق الزمني لمشروعك؟',
      placeholder: 'e.g., Global 24/7 support, Business hours EST, Real-time responses...',
      placeholderAr: 'مثال: دعم عالمي على مدار الساعة، ساعات العمل بتوقيت شرق أمريكا، استجابات فورية...',
      aiSuggestion: 'Consider time zones, working hours, response time expectations, and any time-sensitive requirements.',
      aiSuggestionAr: 'فكر في المناطق الزمنية وساعات العمل وتوقعات وقت الاستجابة وأي متطلبات حساسة للوقت.',
      promptTemplate: 'Help me optimize this temporal context for an AI project: "{answer}". Suggest improvements for better time management.'
    },
    {
      id: 'language',
      question: 'What languages should your AI system support?',
      questionAr: 'ما هي اللغات التي يجب أن يدعمها نظام الذكاء الاصطناعي؟',
      placeholder: 'e.g., English primary, Arabic secondary, Multilingual support...',
      placeholderAr: 'مثال: الإنجليزية أساسية، العربية ثانوية، دعم متعدد اللغات...',
      type: 'text' as const,
      aiSuggestion: 'Consider your target audience, regional requirements, and the complexity of multilingual support.',
      aiSuggestionAr: 'فكر في جمهورك المستهدف والمتطلبات الإقليمية وتعقيد الدعم متعدد اللغات.',
      promptTemplate: 'Analyze this language requirement for an AI system: "{answer}". Suggest implementation strategies.'
    },
    {
      id: 'location',
      question: 'What geographic regions or locations will this project serve?',
      questionAr: 'ما هي المناطق الجغرافية أو المواقع التي سيخدمها هذا المشروع؟',
      placeholder: 'e.g., Middle East, North America, Global, Specific cities...',
      placeholderAr: 'مثال: الشرق الأوسط، أمريكا الشمالية، عالمي، مدن محددة...',
      aiSuggestion: 'Think about regional regulations, cultural differences, and infrastructure requirements.',
      aiSuggestionAr: 'فكر في اللوائح الإقليمية والاختلافات الثقافية ومتطلبات البنية التحتية.',
      promptTemplate: 'Help me understand the geographic implications of this scope: "{answer}". What should I consider?'
    }
  ];

  const handleFieldChange = (field: keyof typeof contextMap, value: string) => {
    updateContextMap({ [field]: value });
  };

  return (
    <ModuleLayout
      title="Context Map"
      titleAr="خريطة السياق"
      subtitle="Define the contextual framework for your AI project"
      subtitleAr="حدد الإطار السياقي لمشروع الذكاء الاصطناعي"
      emoji="🗺️"
      backLink={{
        href: "/project-definition",
        label: "← Back to Project Definition",
        labelAr: "← العودة لتعريف المشروع"
      }}
      nextLink={{
        href: "/emotional-tone",
        label: "Next: Emotional Tone →",
        labelAr: "التالي: النبرة العاطفية ←"
      }}
      rightPanel={
        <OutputPanel
          moduleData={contextMap}
          moduleName="Context Map"
          moduleNameAr="خريطة السياق"
        />
      }
    >
      <div className="space-y-6">
        {questions.map((question) => (
          <SmartQuestion
            key={question.id}
            id={question.id}
            question={question.question}
            questionAr={question.questionAr}
            placeholder={question.placeholder}
            placeholderAr={question.placeholderAr}
            value={contextMap[question.id as keyof typeof contextMap] || ''}
            onChange={(value) => handleFieldChange(question.id as keyof typeof contextMap, value)}
            type={question.type}
            aiSuggestion={question.aiSuggestion}
            aiSuggestionAr={question.aiSuggestionAr}
            promptTemplate={question.promptTemplate}
          />
        ))}
      </div>
    </ModuleLayout>
  );
}
